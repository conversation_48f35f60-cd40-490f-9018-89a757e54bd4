//
//  IMYNASearchHomeVC.m
//  ZZIMYMain
//
//  Created by ljh on 2023/8/7.
//

#import "IMYNASearchHomeVC.h"
#import "SYHomeSearchAssociateVC.h"
#import "IMYNASearchHistoryView.h"
#import "IMYNASearchGuessYouView.h"
#import "IMYNASearchTabContainerView.h"
#import "IMYNASearchHistoryModel.h"
#import <IOC-Protocols/IOCAppInfo.h>
#import <IMYAdvertisement/IMYAdvertisementSDK.h>
#import "IMYNAInputSearchBar.h"
#import "SYSearchResultWithH5VC.h"
#import "IMYNativeFeedbackManager.h"

static NSString * const kIMYKVSearchHistoryKey = @"search.history.keys";
static NSString * const kIMYKVSearchHistoryModelsKey = @"search.history.models";
static NSString * const kIMYSearchHomeOnAssociateChangeKey = @"kIMYSearchHomeOnAssociateChangeKey";

@interface IMYNASearchHomeVC () <UIScrollViewDelegate>

// 运营词类型
@property (nonatomic, assign) SYSearchWordType words_type;
// AB实验：联想词直达小工具加入搜索历史实验值
@property (nonatomic, assign) NSInteger hiszdExperimentValue;
@property (nonatomic, strong) IMYNAInputSearchBar *searchBar;
@property (nonatomic, strong) IMYNAInputAnimationView *animationBar;

@property (nonatomic, strong) SYHomeSearchAssociateVC *associateVC;

// 当前搜索词
@property (nonatomic, copy) NSString *keyword;

@property (nonatomic, copy) NSString *lastTextFiledText;
@property (nonatomic, copy) NSString *placeholderText;

// 左滑返回使用的占位图
@property (nonatomic, strong) UIView *panPresentView;
@property (nonatomic, strong) UIView *panShadowView;

// 具体UI
@property (nonatomic, strong) UIScrollView *mainScrollView;
@property (nonatomic, strong) IMYNASearchHistoryView *historyView;
@property (nonatomic, strong) IMYNASearchGuessYouView *guessYouView;
@property (nonatomic, strong) IMYNASearchTabContainerView *tabContainerView;

// 批量曝光用
@property (nonatomic, strong) NSMutableArray<NSString *> *historyExposuredKeys;
@property (nonatomic, strong) NSMutableArray<NSString *> *guessYouExposuredKeys;

/// 加载失败用
@property (nonatomic, strong) IMYCaptionView *captionView;

// 搜索接口下发的数据
@property (nonatomic, copy) NSArray *origGuessKeyModels;
@property (nonatomic, copy) NSArray *origHotsportsKeyModels;
// 迭代8.95：热门讨论数据
@property (nonatomic, copy) NSArray<IMYNASearchHotDiscussionKeyModel *> *origHotDiscussionKeyModels;

// 广告接口下发的数据
@property (nonatomic, strong) id<IMYITableViewAdManager> adManager;
@property (nonatomic, strong) IMYAdSignal *adShowSignal; // 广告曝光回调
@property (nonatomic, strong) IMYAdSignal *adClickSignal; // 广告点击回调
@property (nonatomic, copy) NSArray *adGuessKeyModels;
@property (nonatomic, copy) NSArray *adHotsportsKeyModels;

// 是否使用新样式（带返回按钮、右边是搜索按钮）
@property (nonatomic, assign) BOOL usingNewSearchStyle;

// 搜索栏样式值（0-老样式，1-实验组1，2-实验组2）
@property (nonatomic, assign) NSInteger searchBarStyleValue;

// 输入框清空按钮是否曝光过
@property (nonatomic, assign) BOOL isExposuredClearButton;

// 输入框是否需要获取焦点
@property (nonatomic, assign) BOOL isSearchBarInputing;

// 是否搜索首页
@property (nonatomic, assign) BOOL isSearchHomePage;

// 保存原始的图标类型，用于用户结束输入时恢复图标
@property (nonatomic, assign) NSInteger originalIconType;

// 是否需要在页面返回时聚焦搜索栏（收到SearchBackgroundWordChangedNotification后设置）
@property (nonatomic, assign) BOOL shouldFocusSearchBarOnReturn;

// 底纹词曝光去重用（避免重复上报）
@property (nonatomic, copy) NSString *lastExposedPlaceholderKeyword;

@end


@implementation IMYNASearchHomeVC

#pragma mark - 属性重写

/**
 * 重写searchPlaceholder的setter方法
 * 支持字典内省，如果传入字符串则转换为8.93.0版本字典格式
 */
- (void)setSearchPlaceholder:(id)searchPlaceholder {
    if (!searchPlaceholder) {
        _searchPlaceholder = nil;
        return;
    }
    
    // 如果传入的是字典，直接使用
    if ([searchPlaceholder isKindOfClass:[NSDictionary class]]) {
        _searchPlaceholder = searchPlaceholder;
        return;
    }
    
    // 如果传入的是字符串，转换为8.93.0版本的字典格式
    if ([searchPlaceholder isKindOfClass:[NSString class]]) {
        NSString *keyword = (NSString *)searchPlaceholder;
        if (keyword.length > 0) {
            _searchPlaceholder = @{
                @"keyword": keyword,
                @"icon_type": @(0),          // 默认无图标
                @"scheme_uri": @"",          // 默认无跳转链接
                @"data_type": @(1)           // 默认类型为1
            };
        } else {
            _searchPlaceholder = nil;
        }
        return;
    }
    
    // 其他情况设为nil
    _searchPlaceholder = nil;
}

/**
 * 重写defaultSearchPlaceholder的setter方法
 * 防止外部传入字典对象导致崩溃
 */
- (void)setDefaultSearchPlaceholder:(NSString *)defaultSearchPlaceholder {
    if (!defaultSearchPlaceholder) {
        _defaultSearchPlaceholder = nil;
        return;
    }
    
    // 如果传入的是字符串，直接使用
    if ([defaultSearchPlaceholder isKindOfClass:[NSString class]]) {
        _defaultSearchPlaceholder = [defaultSearchPlaceholder copy];
        return;
    }
    
    // 如果传入的是字典对象（8.93.0版本的head_search_words），提取keyword字段
    if ([defaultSearchPlaceholder isKindOfClass:[NSDictionary class]]) {
        NSDictionary *wordDict = (NSDictionary *)defaultSearchPlaceholder;
        NSString *keyword = wordDict[@"keyword"];
        if ([keyword isKindOfClass:[NSString class]] && keyword.length > 0) {
            _defaultSearchPlaceholder = [keyword copy];
            return;
        }
    }
    
    // 如果传入的是数组，取第一个元素
    if ([defaultSearchPlaceholder isKindOfClass:[NSArray class]]) {
        NSArray *array = (NSArray *)defaultSearchPlaceholder;
        if (array.count > 0) {
            id firstItem = array[0];
            if ([firstItem isKindOfClass:[NSString class]]) {
                _defaultSearchPlaceholder = [firstItem copy];
                return;
            } else if ([firstItem isKindOfClass:[NSDictionary class]]) {
                NSDictionary *wordDict = (NSDictionary *)firstItem;
                NSString *keyword = wordDict[@"keyword"];
                if ([keyword isKindOfClass:[NSString class]] && keyword.length > 0) {
                    _defaultSearchPlaceholder = [keyword copy];
                    return;
                }
            }
        }
    }
    
    // 其他情况转为字符串处理
    NSString *description = [defaultSearchPlaceholder description];
    _defaultSearchPlaceholder = description.length > 0 ? [description copy] : nil;
}

/**
 * 重写keyword的setter方法
 * 防止外部传入字典对象导致崩溃
 */
- (void)setKeyword:(NSString *)keyword {
    if (!keyword) {
        _keyword = nil;
        return;
    }
    
    // 如果传入的是字符串，直接使用
    if ([keyword isKindOfClass:[NSString class]]) {
        _keyword = [keyword copy];
        return;
    }
    
    // 如果传入的是字典对象（8.93.0版本的head_search_words），提取keyword字段
    if ([keyword isKindOfClass:[NSDictionary class]]) {
        NSDictionary *wordDict = (NSDictionary *)keyword;
        NSString *keywordStr = wordDict[@"keyword"];
        if ([keywordStr isKindOfClass:[NSString class]] && keywordStr.length > 0) {
            _keyword = [keywordStr copy];
            return;
        }
    }
    
    // 如果传入的是数组，取第一个元素
    if ([keyword isKindOfClass:[NSArray class]]) {
        NSArray *array = (NSArray *)keyword;
        if (array.count > 0) {
            id firstItem = array[0];
            if ([firstItem isKindOfClass:[NSString class]]) {
                _keyword = [firstItem copy];
                return;
            } else if ([firstItem isKindOfClass:[NSDictionary class]]) {
                NSDictionary *wordDict = (NSDictionary *)firstItem;
                NSString *keywordStr = wordDict[@"keyword"];
                if ([keywordStr isKindOfClass:[NSString class]] && keywordStr.length > 0) {
                    _keyword = [keywordStr copy];
                    return;
                }
            }
        }
    }
    
    // 其他情况转为字符串处理
    NSString *description = [keyword description];
    _keyword = description.length > 0 ? [description copy] : nil;
}

+ (NSString *)getKVAPIDataCacheKey:(SYSearchFromType)from {
    return [NSString stringWithFormat:@"search.home.data-%ld", from];
}

+ (NSString *)getKVAPITimeCacheKey:(SYSearchFromType)from {
    return [NSString stringWithFormat:@"search.home.time-%ld", from];
}

#pragma mark - 历史记录模型存储

+ (void)saveHistoryModels:(NSArray<IMYNASearchHistoryModel *> *)historyModels {
    if (!historyModels) {
        return;
    }
    
    // 序列化模型数组
    NSMutableArray *serializedArray = [NSMutableArray array];
    for (IMYNASearchHistoryModel *model in historyModels) {
        id dict = [model imy_jsonObject];
        if (dict) {
            [serializedArray addObject:dict];
        }
    }

    // 统一保存到模型格式存储
    [[IMYKV defaultKV] setArray:serializedArray forKey:kIMYKVSearchHistoryModelsKey];
}

+ (NSArray<IMYNASearchHistoryModel *> *)loadHistoryModels {
    // 检查新存储格式是否已经存在（包括空数组的情况）
    if ([[IMYKV defaultKV] containsKey:kIMYKVSearchHistoryModelsKey]) {
        // 新存储格式已存在，直接加载（即使是空数组也不进行数据迁移）
        NSArray *serializedArray = [[IMYKV defaultKV] arrayForKey:kIMYKVSearchHistoryModelsKey];
        NSMutableArray *historyModels = [NSMutableArray array];
        for (id dict in serializedArray) {
            IMYNASearchHistoryModel *model = [IMYNASearchHistoryModel yy_modelWithJSON:dict];
            if (model) {
                [historyModels addObject:model];
            }
        }
        return [historyModels copy];
    }

    // 数据迁移：从字符串数组迁移到模型格式（一次性迁移）
    NSArray *historyKeys = [[IMYKV defaultKV] arrayForKey:kIMYKVSearchHistoryKey];
    if (!historyKeys) {
        // 迁移h5的搜索记录
        NSDictionary *map = [[IMYURIManager shareURIManager] runActionAndSyncResultWithPath:@"map/get" params:@{@"keys":@[@"SEARCH_HISTORY"]}];
        NSArray *h5keys = [map[@"SEARCH_HISTORY"] imy_jsonObject];
        historyKeys = h5keys ?: @[];
    }

    // 转换为模型数组
    NSMutableArray *historyModels = [NSMutableArray array];
    for (NSString *key in historyKeys) {
        if ([key isKindOfClass:[NSString class]] && key.length > 0) {
            [historyModels addObject:[IMYNASearchHistoryModel modelWithKeyword:key]];
        }
    }

    // 保存转换后的模型数据，完成迁移
    [self saveHistoryModels:historyModels];

    return [historyModels copy];
}

/// 8.95：将一条历史记录模型置顶并保存
/// 新模型置顶，移除相同 keyword 的旧项；仅做存储层处理
+ (void)saveHistoryModelToTop:(IMYNASearchHistoryModel *)historyModel {
    if (![historyModel isKindOfClass:IMYNASearchHistoryModel.class]) {
        return;
    }
    NSString *keyword = historyModel.keyword;
    if (![keyword isKindOfClass:NSString.class] || keyword.length == 0) {
        return;
    }
    // 读取现有历史（模型格式）
    NSArray<IMYNASearchHistoryModel *> *origModels = [self loadHistoryModels] ?: @[];
    NSMutableArray<IMYNASearchHistoryModel *> *mutableArray = [NSMutableArray arrayWithCapacity:(origModels.count + 1)];
    // 新模型置顶
    [mutableArray addObject:historyModel];
    // 过滤相同 keyword 的旧项
    for (IMYNASearchHistoryModel *existingModel in origModels) {
        if (![existingModel.keyword isKindOfClass:NSString.class]) {
            continue;
        }
        if (![existingModel.keyword isEqualToString:keyword]) {
            [mutableArray addObject:existingModel];
        }
    }
    // 统一保存
    [self saveHistoryModels:mutableArray];
}

#pragma mark - 8.93.0版本：head_search_words对象数组支持

/**
 * 处理8.93.0版本的head_search_words对象数组
 * 提取keyword字段为其他组件提供字符串数组
 */
- (void)preprocessHeadSearchWords:(NSMutableDictionary *)searchSuggestWords {
    if (!searchSuggestWords) {
        return;
    }

    NSArray *headSearchWords = searchSuggestWords[@"head_search_words"];
    if (![headSearchWords isKindOfClass:[NSArray class]] || headSearchWords.count == 0) {
        return;
    }

    // 提取keyword字段创建字符串数组，为其他组件（如IMYCKSearchHelper）提供兼容性
    NSMutableArray *keywordArray = [NSMutableArray array];
    for (NSDictionary *wordObj in headSearchWords) {
        if ([wordObj isKindOfClass:[NSDictionary class]]) {
            NSString *keyword = wordObj[@"keyword"];
            if ([keyword isKindOfClass:[NSString class]] && keyword.length > 0) {
                [keywordArray addObject:keyword];
            }
        }
    }

    // 更新searchSuggestWords，为其他组件提供字符串数组
    if (keywordArray.count > 0) {
        searchSuggestWords[@"head_search_words"] = [keywordArray copy];
    }
}

/**
 * 从searchPlaceholder字典中提取keyword
 */
- (NSString *)getKeywordFromSearchPlaceholder {
    if ([self.searchPlaceholder isKindOfClass:[NSDictionary class]]) {
        NSDictionary *placeholderDict = (NSDictionary *)self.searchPlaceholder;
        NSString *keyword = placeholderDict[@"keyword"];
        if ([keyword isKindOfClass:[NSString class]]) {
            return keyword;
        }
    }
    return nil;
}

/**
 * 从searchPlaceholder字典中获取scheme_uri
 */
- (NSString *)getSchemeURIFromSearchPlaceholder {
    if ([self.searchPlaceholder isKindOfClass:[NSDictionary class]]) {
        NSDictionary *placeholderDict = (NSDictionary *)self.searchPlaceholder;
        NSString *schemeURI = placeholderDict[@"scheme_uri"];
        if ([schemeURI isKindOfClass:[NSString class]]) {
            return schemeURI;
        }
    }
    return nil;
}

/**
 * 从searchPlaceholder字典中获取icon_type
 */
- (NSInteger)getIconTypeFromSearchPlaceholder {
    if ([self.searchPlaceholder isKindOfClass:[NSDictionary class]]) {
        NSDictionary *placeholderDict = (NSDictionary *)self.searchPlaceholder;
        NSNumber *iconType = placeholderDict[@"icon_type"];
        if ([iconType isKindOfClass:[NSNumber class]]) {
            return iconType.integerValue;
        }
    }
    return 0; // 默认无图标
}

IMY_KYLIN_FUNC_MAINTAB_ASYNC {
    // 预热搜索内存缓存
    [[IMYKV defaultKV] integerForKey:[IMYNASearchHomeVC getKVAPITimeCacheKey:SYSearchFromTypeAll]];
    [[IMYKV defaultKV] dictionaryForKey:[IMYNASearchHomeVC getKVAPIDataCacheKey:SYSearchFromTypeAll]];

    // 预加载结果页模板
    [SYSearchResultWithH5VC prefetchSearchResultResources];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.navigationBarHidden = YES;
    
    // 搜索栏样式实验配置
    // 值说明：0-老样式，1-实验组1样式（左返回+右搜索黑色），2-实验组2样式（左返回+右搜索红色）
    IMYABTestExperiment *searchStyleExp = [[IMYABTestManager sharedInstance] experimentForKey:@"btnuniify"];
    self.searchBarStyleValue = [searchStyleExp.vars integerForKey:@"btnuniify"];
    self.usingNewSearchStyle = (self.searchBarStyleValue > 0);
    
    // 联想词直达小工具加入搜索历史AB实验
    IMYABTestExperiment *exp = [[IMYABTestManager sharedInstance] experimentForKey:@"hiszd"];
    self.hiszdExperimentValue = [exp.vars integerForKey:@"hiszd"];
    
    
    // 搜索唯一性标识
    if (!self.search_key.length) {
        self.search_key = [NSString stringWithFormat:@"%@_%.0lf", [IMYPublicAppHelper shareAppHelper].userid, IMYDateTimeIntervalSince1970() * 1000];
    }
    
    // 来自广告
    if (self.showAssociateWithKey) {
        self.info_type = 1;
    }
    
    // 初始化UI和数据
    [self setupUI];
    [self setupLoadAnimation];
    [self setupLocalData];
    [self resetScrollViewContentSize];
    
    [self requestData];
    [self requestAd];
    [self preloadFeedbackData];
    
    // 键盘遮挡曝光
    self.imyut_exposureStatus.enableKeyboardCoverOut = YES;
    // 是否搜索首页
    self.isSearchHomePage = self.navigationController.viewControllers.count <= 1;
    // 搜索首页才有的逻辑
    if (self.isSearchHomePage) {
        // 入口页点击搜索框埋点。 反正点击后都是进入这个页面，所以就放这里埋
        //【【ios】亲友模式，搜索入口重复上报】https://www.tapd.meiyou.com/21039721/bugtrace/bugs/view?bug_id=1121039721001320323 重复上报此处关闭，另外一处在IMYCKDynamicNavBarManager
        //        [self postEntranceClickSearchBarEvent];
        
        // 1秒后在允许曝光
        [self.imyut_exposureStatus resetActivedWithDelayTime:1];
        
        // 如果是present 上的，增加左滑返回手势
        if (self.presentingViewController) {
            [self addPanDismissGestureRecognizer];
        }
    }
    
    // 时间来不及了，怎么方便怎么来。。。 ResultVC 发出
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(onTextFieldResultShouldClearAction:) name:IMYSearchH5ResultClearNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(onResultPageWithSearchAction:) name:IMYSearchH5KeywordChangedNotification object:nil];
    
    // 监听搜索背景词变更通知 - 使用RAC方式
    @weakify(self);
    [[[[[NSNotificationCenter defaultCenter] rac_addObserverForName:@"SearchBackgroundWordChangedNotification" object:nil] takeUntil:self.rac_willDeallocSignal] deliverOnMainThread] subscribeNext:^(NSNotification * _Nullable notification) {
        @strongify(self);
        [self onSearchBackgroundWordChanged:notification];
    }];

    // 修复键盘遮挡曝光上报 - 设置子控制器
    [self setupChildViewControllers];
}

- (void)setupLoadAnimation {
    if (!self.isSearchHomePage || !self.presentingViewController) {
        return;
    }
    
    CGRect beginFrame = CGRectZero;
    CGRect endFrame = self.searchBar.searchBGView.frame;
    
    if (self.befromBarFrame.length > 0) {
        // 有具体坐标系的动画
        beginFrame = CGRectFromString(self.befromBarFrame);
        beginFrame.origin.y -= [IMYSystem screenStatusBarHeight];
        beginFrame.size.height = 36;
    } else {
        // 默认动画
        NSInteger left = 34;
        NSInteger right = 34;
        beginFrame = endFrame;
        beginFrame.origin.x += left;
        beginFrame.size.width -= (right + left);
    }
    
    IMYNAInputAnimationView *animationBar = [IMYNAInputAnimationView new];
    animationBar.textLabel.text = self.searchBar.textField.placeholder;
    animationBar.frame = beginFrame;

    // 8.93.0版本：设置图标标签，保持动画连续性
    NSInteger iconType = [self getIconTypeFromSearchPlaceholder];
    [animationBar updateIconTagWithType:iconType];

    [self.view addSubview:animationBar];
    
    self.searchBar.alpha = 0.01;
    self.mainScrollView.alpha = 0.01;
    [self.searchBar hideSearchSubviews];
    [UIView animateWithDuration:0.25
                     animations:^{
        animationBar.frame = endFrame;
        self.searchBar.alpha = 1;
        self.mainScrollView.alpha = 1;
    } completion:^(BOOL finished) {
        [animationBar removeFromSuperview];
        [self.searchBar showSearchSubviews];
        self.animationBar = animationBar;
    }];
}

- (void)setupUI {
    // 顶部搜索栏
    [self.view addSubview:self.searchBar];
    
    // 高度有限 就不用TableView了
    self.mainScrollView = [UIScrollView new];
    self.mainScrollView.frame = CGRectMake(0, self.searchBar.imy_bottom, SCREEN_WIDTH, SCREEN_HEIGHT - self.searchBar.imy_bottom - SCREEN_STATUSBAR_HEIGHT);
    self.mainScrollView.delegate = self;
    self.mainScrollView.showsVerticalScrollIndicator = NO;
    [self.mainScrollView imy_setBackgroundColorForKey:kIMY_BG];
    [self.view addSubview:self.mainScrollView];
    
    self.historyView = [IMYNASearchHistoryView new];
    [self.mainScrollView addSubview:self.historyView];
    
    self.guessYouView = [IMYNASearchGuessYouView new];
    // 设置猜你想搜布局样式（基于AB实验）
    IMYABTestExperiment *guessLayoutExp = [[IMYABTestManager sharedInstance] experimentForKey:@"guessdislike"];
    NSInteger guessLayoutValue = [guessLayoutExp.vars integerForKey:@"guess"];
    // 将实验值映射到布局样式：1=原始样式，2=3行2列，3=4行2列
    IMYNASearchGuessYouLayoutStyle layoutStyle = IMYNASearchGuessYouLayoutStyleOriginal;
    if (guessLayoutValue == 2) {
        layoutStyle = IMYNASearchGuessYouLayoutStyleGrid3x2;
    } else if (guessLayoutValue == 3) {
        layoutStyle = IMYNASearchGuessYouLayoutStyleGrid4x2;
    }
    self.guessYouView.layoutStyle = layoutStyle;
    [self.mainScrollView addSubview:self.guessYouView];

    self.tabContainerView = [IMYNASearchTabContainerView new];
    [self.mainScrollView addSubview:self.tabContainerView];
    
    self.captionView = [IMYCaptionView addToView:self.mainScrollView show:NO];
    [self.captionView imy_setBackgroundColorForKey:kIMY_BG];
    self.captionView.imy_height = 300;
    @weakify(self);
    self.captionView.retryBlock = ^{
        @strongify(self);
        self.captionView.state = IMYCaptionViewStateHidden;
        self.tabContainerView.alpha = 1;
        if (!IMYHIVE_BINDER(IOCAppInfo).isClosedSearchRecommend) {
            // 没有关闭个性化推荐，才显示
            self.guessYouView.alpha = 1;
        }
        [self requestData];
        if (!self.adHotsportsKeyModels.count) {
            [self requestAd];
        }
    };
    // 进行网络监听，无数据情况下自动重试
    [[[IMYNetState networkChangedSignal] skip:1] subscribeNext:^(id  _Nullable x) {
        @strongify(self);
        if (!self || ![IMYNetState networkEnable]) {
            // 还是无网络
            return;
        }
        // 刷新UI
        if (self.captionView.state != IMYCaptionViewStateHidden) {
            self.captionView.state = IMYCaptionViewStateHidden;
            self.tabContainerView.alpha = 1;
            if (!IMYHIVE_BINDER(IOCAppInfo).isClosedSearchRecommend) {
                // 没有关闭个性化推荐，才显示
                self.guessYouView.alpha = 1;
            }
        }
        // 无数据，就进行请求
        if (!self.origHotsportsKeyModels.count) {
            [self requestData];
        }
        if (!self.adHotsportsKeyModels.count) {
            [self requestAd];
        }
    }];
    
    // 点击空白区收起键盘
    void(^onEmptyDidClickedBlock)(void) = ^{
        @strongify(self);
        [self.searchBar.textField resignFirstResponder];
    };
    self.historyView.onEmptyDidClickedBlock = onEmptyDidClickedBlock;
    self.guessYouView.onEmptyDidClickedBlock = onEmptyDidClickedBlock;
    self.tabContainerView.onEmptyDidClickedBlock = onEmptyDidClickedBlock;
    
    // 高度变化
    void(^onHeightDidChangedBlock)(BOOL anim) = ^(BOOL anim) {
        @strongify(self);
        if (anim) {
            [UIView animateWithDuration:0.2 animations:^{
                self.guessYouView.imy_top = self.historyView.imy_bottom;
                self.tabContainerView.imy_top = self.guessYouView.imy_bottom;
                self.captionView.imy_top = self.historyView.imy_bottom;
            }];
        } else {
            self.guessYouView.imy_top = self.historyView.imy_bottom;
            self.tabContainerView.imy_top = self.guessYouView.imy_bottom;
            self.captionView.imy_top = self.historyView.imy_bottom;
        }
        [self resetScrollViewContentSize];
    };
    self.historyView.onHeightDidChangedBlock = onHeightDidChangedBlock;
    self.guessYouView.onHeightDidChangedBlock = onHeightDidChangedBlock;
    self.tabContainerView.onHeightDidChangedBlock = onHeightDidChangedBlock;
    
    // 历史记录 - 统一使用模型回调机制（AB实验差异在业务逻辑层处理）
    self.historyView.onHistoryModelsDidChangedBlock = ^(NSArray<IMYNASearchHistoryModel *> * _Nonnull historyModels) {
        // 统一保存模型格式
        [IMYNASearchHomeVC saveHistoryModels:historyModels];
    };
    self.historyView.onHistoryModelDidPressedBlock = ^(IMYNASearchHistoryModel *historyModel) {
        @strongify(self);
        [self onClickedWithHistoryModel:historyModel];
    };
    self.historyView.onKeyDidExposuredBlock = ^(NSString *key) {
        @strongify(self);
        [self onExposuredWithHistoryKey:key];
    };
    
    // 猜你想搜
    if (IMYHIVE_BINDER(IOCAppInfo).isClosedSearchRecommend) {
        // 个性化推荐被关闭
        [self.guessYouView setupWithGuessKeyModels:nil animated:NO];
    }
    self.guessYouView.onKeyDidPressedBlock = ^(IMYNASearchGuessYouKeyModel *keyModel) {
        @strongify(self);
        [self onClickedWithGuessYouKey:keyModel];
    };
    self.guessYouView.onKeyDidExposuredBlock = ^(IMYNASearchGuessYouKeyModel * _Nonnull keyModel) {
        @strongify(self);
        [self onExposuredWithGuessYouKey:keyModel];
    };
    
    // 负反馈按钮回调
    self.guessYouView.onActionButtonPressedBlock = ^{
        @strongify(self);
        [self onGuessYouFeedbackButtonPressed];
    };
    
    // 热榜
    self.tabContainerView.onHotspotsKeyDidPressedBlock = ^(IMYNASearchHotspotsKeyModel *keyModel) {
        @strongify(self);
        [self onClickedWithHotspotsKey:keyModel];
    };
    self.tabContainerView.onHotspotsKeyDidExposuredBlock = ^(IMYNASearchHotspotsKeyModel * _Nonnull keyModel) {
        @strongify(self);
        [self onExposuredWithHotspotsKey:keyModel];
    };

    // 热门讨论回调
    self.tabContainerView.onHotDiscussionKeyDidPressedBlock = ^(IMYNASearchHotDiscussionKeyModel *keyModel) {
        @strongify(self);
        [self onClickedWithHotDiscussionKey:keyModel];
    };
    self.tabContainerView.onHotDiscussionKeyDidExposuredBlock = ^(IMYNASearchHotDiscussionKeyModel *keyModel) {
        @strongify(self);
        [self onExposuredWithHotDiscussionKey:keyModel];
    };
    NSString *backgroundWords = self.keyword ?: self.searchPlaceholder[@"keyword"];
    self.searchBar.textField.imyut_eventInfo.eventName = [NSString stringWithFormat:@"search-background-%@", backgroundWords];
    self.searchBar.textField.imyut_eventInfo.exposuredBlock = ^(__kindof UIView *view, NSDictionary *params) {
        @strongify(self);
        
        [self postPlaceholderExposureForKeyword:backgroundWords sourceModel:self.searchPlaceholder];
    };
}

- (void)resetScrollViewContentSize {
    CGFloat lastBottom = MAX(self.tabContainerView.imy_bottom, self.mainScrollView.imy_height + 1);
    self.mainScrollView.contentSize = CGSizeMake(1, lastBottom + SCREEN_TABBAR_SAFEBOTTOM_MARGIN);
}

- (void)setupLocalData {
    // 从缓存中载入推荐词数据数据
    self.searchSuggestWords = [[SYH5SearchManager manager] loadSearchSuggestWordsWithFrom:self.from];
    
    // 8.93.0版本：处理head_search_words对象数组，为其他组件提供兼容的字符串数组
    if (self.searchSuggestWords) {
        NSMutableDictionary *mutableSearchSuggestWords = [self.searchSuggestWords mutableCopy];
        [self preprocessHeadSearchWords:mutableSearchSuggestWords];
        self.searchSuggestWords = [mutableSearchSuggestWords copy];
    }
    // 搜索历史 - 统一使用模型格式加载（AB实验仅控制UI显示方式）
    NSArray<IMYNASearchHistoryModel *> *historyModels = [IMYNASearchHomeVC loadHistoryModels];
    
    //都要设置，为空时才不展示
    [self.historyView setupWithHistoryModels:historyModels animated:NO];
    
    // 先读取接口本地缓存，提高页面显示速度
    NSInteger apiTime = [[IMYKV defaultKV] integerForKey:[IMYNASearchHomeVC getKVAPITimeCacheKey:self.from]];
    NSInteger diffTime = CFAbsoluteTimeGetCurrent() - apiTime;
    if (diffTime < 86400) {
        // 缓存有效期1天
        NSDictionary *apiCache = [[IMYKV defaultKV] dictionaryForKey:[IMYNASearchHomeVC getKVAPIDataCacheKey:self.from]];
        // 模块名称配置
        [self setupModuleNames:apiCache[@"module_config"]];
        // 猜你想搜
        NSArray *guessKeyModels = [apiCache[@"give_words"] toModels:IMYNASearchGuessYouKeyModel.class];
        if (guessKeyModels.count > 0 && !IMYHIVE_BINDER(IOCAppInfo).isClosedSearchRecommend) {
            self.origGuessKeyModels = guessKeyModels;
            [self setupGuessYouWithKeyModels];
        }
        // 搜索热榜
        NSArray *hotKeyModels = [apiCache[@"rank_list"][@"words"] toModels:IMYNASearchHotspotsKeyModel.class];
        // 迭代8.95：热门讨论缓存数据
        NSArray *hotDiscussionKeyModels = [apiCache[@"rank_list_topic"][@"words"] toModels:IMYNASearchHotspotsKeyModel.class];

        // 迭代8.95：分别处理热门搜索和热门讨论的缓存数据
        if (hotKeyModels.count > 0) {
            self.origHotsportsKeyModels = hotKeyModels;
            [self setupHotspotsWithKeyModels];
        }
        // 迭代8.95：处理热门讨论缓存数据（包括实验组判断）
        self.origHotDiscussionKeyModels = hotDiscussionKeyModels ?: @[];
        [self setupHotDiscussionWithKeyModels];
    }
}

- (void)setupModuleNames:(NSArray *)module_config {
    NSArray *modules = module_config[@"modules"];
    for (NSDictionary *map in modules) {
        NSArray *datas = map[@"data"];
        for (NSDictionary *config in datas) {
            // 模块类型：1模块-猜你像搜 2模块-搜索热点 3模块-搜索历史 4广告 5功能 6 专家问诊榜单 7 百科榜单
            NSInteger type = [config[@"category_type"] integerValue];
            if (type == 1) {
                self.guessYouView.name = config[@"tab_name"];
            } else if (type == 2) {
                self.tabContainerView.name = config[@"tab_name"];
            } else if (type == 3) {
                self.historyView.name = config[@"tab_name"];
            }
        }
    }
}

- (void)setupGuessYouWithKeyModels {
    if (IMYHIVE_BINDER(IOCAppInfo).isClosedSearchRecommend) {
        return;
    }
    if (!self.origGuessKeyModels) {
        // 搜索接口还未回来
        return;
    }
    NSMutableArray *mergeArray = [self.origGuessKeyModels mutableCopy];
    if (mergeArray.count > 0) {
        // 必须接口有数据才插入广告内容
        for (IMYNASearchGuessYouKeyModel *adKeyModel in self.adGuessKeyModels) {
            if (mergeArray.count > adKeyModel.ad_index) {
                [mergeArray insertObject:adKeyModel atIndex:adKeyModel.ad_index];
            } else {
                [mergeArray addObject:adKeyModel];
            }
        }
    }
    BOOL needAnim = self.isViewWillAppeared;
    [self.guessYouView setupWithGuessKeyModels:mergeArray animated:needAnim];
}

- (void)setupHotspotsWithKeyModels {
    if (!self.origHotsportsKeyModels) {
        // 搜索接口还未回来
        return;
    }
    NSMutableArray *mergeArray = [self.origHotsportsKeyModels mutableCopy];
    if (mergeArray.count > 0) {
        // 必须接口有数据才插入广告内容
        for (IMYNASearchHotspotsKeyModel *adKeyModel in self.adHotsportsKeyModels) {
            // 搜索热点广告过滤规则：搜索内容数量+广告已插入内容数量＞=广告位正在插入位置。
            // 展示广告内容，小于广告插入位置则不展示
            if (mergeArray.count >= adKeyModel.ad_index) {
                [mergeArray insertObject:adKeyModel atIndex:adKeyModel.ad_index];
            }
        }
    }
    BOOL needAnim = self.isViewWillAppeared;
    [self.tabContainerView setupWithHotspotsKeyModels:mergeArray animated:needAnim];

    // 迭代8.95：设置热门讨论数据（使用rank_list_topic数据）
    [self setupHotDiscussionWithKeyModels];
}

#pragma mark - 修复键盘遮挡曝光上报 - 子控制器管理

- (void)setupChildViewControllers {
    // 确保 IMYPageViewController 被添加为子控制器
    if (self.tabContainerView.pageViewController &&
        ![self.childViewControllers containsObject:self.tabContainerView.pageViewController]) {
        [self addChildViewController:self.tabContainerView.pageViewController];
        // 设置enableKeyboardCoverOut为YES
        self.tabContainerView.pageViewController.imyut_exposureStatus.enableKeyboardCoverOut = YES;
    }

    // 确保懒加载的controller被添加为子控制器
    UIViewController *hotspotsController = [self.tabContainerView hotspotsController];
    if (hotspotsController && ![self.childViewControllers containsObject:hotspotsController]) {
        [self addChildViewController:hotspotsController];
    }

    UIViewController *hotDiscussionController = [self.tabContainerView hotDiscussionController];
    if (hotDiscussionController && ![self.childViewControllers containsObject:hotDiscussionController]) {
        [self addChildViewController:hotDiscussionController];
    }
}

// 迭代8.95：设置热门讨论数据
- (void)setupHotDiscussionWithKeyModels {
    BOOL hasHotDiscussionData = (self.origHotDiscussionKeyModels && self.origHotDiscussionKeyModels.count > 0);
    BOOL needAnim = self.isViewWillAppeared;

    // 迭代8.95：动态控制tab显示状态
    [self.tabContainerView updateTabVisibilityWithHotDiscussionAvailable:hasHotDiscussionData animated:needAnim];

    if (hasHotDiscussionData) {
        // 有热门讨论数据，设置数据
        [self.tabContainerView setupWithHotDiscussionKeyModels:self.origHotDiscussionKeyModels animated:needAnim];
    } else {
        // 没有热门讨论数据，清空数据
        [self.tabContainerView setupWithHotDiscussionKeyModels:@[] animated:needAnim];
    }

    // 修复键盘遮挡曝光上报 - 确保子控制器被正确添加
    [self setupChildViewControllers];
}

- (void)requestData {
    const SYSearchFromType from = self.from;
    NSDictionary *params = @{
        @"from" : @(from),
        @"pos_id" : @(self.pos_id),
    };
    @weakify(self);
    [[IMYServerRequest getPath:@"v2/search_suggest" host:search_seeyouyima_com params:params headers:nil] subscribeNext:^(IMYHTTPResponse *x) {
        NSDictionary *apiData = x.responseObject;
        
        // 8.93.0版本：处理head_search_words对象数组，为其他组件提供兼容的字符串数组
        NSMutableDictionary *mutableApiData = [apiData mutableCopy];
        @strongify(self);
        [self preprocessHeadSearchWords:mutableApiData];
        apiData = [mutableApiData copy];
        
        NSArray *guessKeyModels = [apiData[@"give_words"] toModels:IMYNASearchGuessYouKeyModel.class];
        NSArray *hotKeyModels = [apiData[@"rank_list"][@"words"] toModels:IMYNASearchHotspotsKeyModel.class];
        // 迭代8.95：解析热门讨论数据
        NSArray *hotDiscussionKeyModels = [apiData[@"rank_list_topic"][@"words"] toModels:IMYNASearchHotspotsKeyModel.class];
        if (hotKeyModels.count > 0 || hotDiscussionKeyModels.count > 0) {
            // 迭代8.95：热门搜索或热门讨论数据解析正常，则缓存接口数据
            [[IMYKV defaultKV] setDictionary:apiData forKey:[IMYNASearchHomeVC getKVAPIDataCacheKey:from]];
            [[IMYKV defaultKV] setInteger:CFAbsoluteTimeGetCurrent() forKey:[IMYNASearchHomeVC getKVAPITimeCacheKey:from]];
        }
        imy_asyncMainBlock(^{
            @strongify(self);
            self.captionView.state = IMYCaptionViewStateHidden;
            [self setupModuleNames:apiData[@"module_config"]];
            self.origGuessKeyModels = guessKeyModels ?: @[];
            self.origHotsportsKeyModels = hotKeyModels ?: @[];
            // 迭代8.95：存储热门讨论数据
            self.origHotDiscussionKeyModels = hotDiscussionKeyModels ?: @[];
            [self setupGuessYouWithKeyModels];
            [self setupHotspotsWithKeyModels];
            // 迭代8.95：设置热门讨论数据
            [self setupHotDiscussionWithKeyModels];
        });
    } error:^(NSError *error) {
        imy_asyncMainBlock(^{
            @strongify(self);
            if (!self.tabContainerView.hotspotsKeys.count) {
                // 无缓存数据，才显示 loading retry
                self.captionView.state = IMYCaptionViewStateRetry;
                self.guessYouView.alpha = 0;
                self.tabContainerView.alpha = 0;
            }
        });
    }];
}

// 请求广告
- (void)requestAd {
    if (!self.adManager) {
        @weakify(self);
        void(^onRefreshDataBlock)(NSArray *) = ^(NSArray *data) {
            @strongify(self);
            [self setupAdData:data];
        };
        NSDictionary *userInfo = @{
            @"custom_flag":@"search_home",
            @"adShowSignal":self.adShowSignal,
            @"adClickSignal":self.adClickSignal,
            @"onRefreshDataBlock":onRefreshDataBlock
        };
        
        IMYAdvertiserInfo *adInfo = [IMYAdvertiserInfo adInfoWithSource:nil page:IMYADPageSearchBar position:0 userInfo:userInfo viewController:nil];
        id<IMYITableViewAdManager> adManager = [[IMYAdFactories getAdManagerFactory] getTableViewAdManagerWithADInfo:adInfo];
        self.adManager = adManager;
    }
    [self.adManager refreshData];
}

- (void)setupAdData:(NSArray *)data {
    NSMutableArray *guessArray = [NSMutableArray array]; // 猜你想搜
    NSMutableArray *hotArray = [NSMutableArray array]; // 热点搜索
    for (NSDictionary *adDic in data) {
        NSInteger position = [[adDic valueForKey:@"position"] integerValue];
        NSInteger index = [[adDic valueForKey:@"index"] integerValue]; // 插入位置0开始
        NSString *content = [adDic valueForKey:@"content"]; // 标题
        NSString *tagTitle = [adDic valueForKey:@"tagTitle"]; // 类型
        NSInteger txt_icon_pos = [[adDic valueForKey:@"txt_icon_pos"] integerValue]; // 标签位置1：前，2：后
        NSString *txt_icon = [adDic valueForKey:@"txt_icon"]; // 标签url
        BOOL isUriPush = [[adDic valueForKey:@"is_uri_push"] boolValue]; // YES:走广告跳转uri，NO：作为搜索关键字
        NSInteger iconType = 0;
        if ([tagTitle isEqualToString:@"广告"]) {
            iconType = 5;
        }
        
        if (position == 6702) {
            // 猜你想说
            IMYNASearchGuessYouKeyModel *keyModel = [[IMYNASearchGuessYouKeyModel alloc] init];
            keyModel.icon_type = iconType;
            keyModel.keyword = content;
            keyModel.ad_model = YES;
            keyModel.ad_isUriPush = isUriPush;
            keyModel.ad_index = index;
            keyModel.ad_icon = txt_icon;
            keyModel.ad_icon_pos = txt_icon_pos;
            [guessArray addObject:keyModel];
        }
        if (position == 6703) {
            // 热点搜索
            IMYNASearchHotspotsKeyModel *keyModel = [[IMYNASearchHotspotsKeyModel alloc] init];
            keyModel.icon = iconType;
            keyModel.show_word = content;
            keyModel.query_word = content;
            keyModel.ad_model = YES;
            keyModel.ad_isUriPush = isUriPush;
            keyModel.ad_index = index;
            keyModel.ad_icon = txt_icon;
            keyModel.ad_icon_pos = txt_icon_pos;
            [hotArray addObject:keyModel];
        }
    }
    // 数据整合
    self.adGuessKeyModels = guessArray;
    self.adHotsportsKeyModels = hotArray;
    // 刷新UI
    [self setupGuessYouWithKeyModels];
    [self setupHotspotsWithKeyModels];
}

#pragma mark - 左滑返回手势

- (void)addPanDismissGestureRecognizer {
    UIPanGestureRecognizer *panGesture = [[UIPanGestureRecognizer alloc] initWithTarget:self action:@selector(handlePanDismissGesture:)];
    panGesture.maximumNumberOfTouches = 1;
    panGesture.delegate = self;
    [self.view addGestureRecognizer:panGesture];
}

- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldRecognizeSimultaneouslyWithGestureRecognizer:(UIGestureRecognizer *)otherGestureRecognizer {
    // 如果另一个手势是 IMYPageViewController 的滑动手势，不允许同时识别
    if ([otherGestureRecognizer isKindOfClass:UIPanGestureRecognizer.class]) {
        // 检查是否是 PageViewController 的手势
        UIView *otherView = otherGestureRecognizer.view;
        if ([otherView isKindOfClass:UIScrollView.class] &&
            [otherView.superview isKindOfClass:NSClassFromString(@"IMYPageViewController")]) {
            return NO; // 不允许同时识别，让 PageViewController 的手势优先
        }
        return YES;
    }
    return NO;
}

- (BOOL)gestureRecognizerShouldBegin:(UIPanGestureRecognizer *)gestureRecognizer {
    // 键盘输入状态
    if (IMYSystem.isShowingKeyboardWindow) {
        [[UIApplication sharedApplication].keyWindow endEditing:YES];
        return NO;
    }
    // 垂直速度更高
    CGPoint velocity = [gestureRecognizer velocityInView:gestureRecognizer.view];
    if (fabs(velocity.y) > fabs(velocity.x)) {
        return NO;
    }

    // 检查是否应该允许侧滑返回（考虑tab状态和滑动方向）
    if (![self shouldAllowPanDismissWithGestureRecognizer:gestureRecognizer]) {
        return NO; // 不允许侧滑返回
    }
    // 响应左滑手势的时候，取消 webview 的滚动手势
    UIScrollView *otherScrollView = self.mainScrollView;
    for (UIPanGestureRecognizer *otherPanGesture in otherScrollView.gestureRecognizers) {
        if ([otherPanGesture isKindOfClass:UIPanGestureRecognizer.class]) {
            if (otherPanGesture.enabled) {
                otherPanGesture.enabled = NO;
                imy_asyncMainBlock(^{
                    otherPanGesture.enabled = YES;
                });
            }
        }
    }
    if (!self.panPresentView) {
        UIViewController *parentVC = self.presentingViewController;
        parentVC = parentVC.tabBarController ?: parentVC.navigationController ?: parentVC;
        self.panPresentView = [parentVC.view snapshotViewAfterScreenUpdates:YES];
    }
    if (!self.panPresentView) {
        // 无法截图，不允许侧滑返回
        return NO;
    }
    // 加阴影
    if (!self.panShadowView) {
        UIView *shadowView = [[UIView alloc] initWithFrame:CGRectMake(-1, 0, 1, self.view.imy_height)];
        shadowView.backgroundColor = self.view.backgroundColor;
        shadowView.layer.shadowOffset = CGSizeMake(-3, 0);
        shadowView.layer.shadowRadius = 3;
        shadowView.layer.shadowOpacity = 1;
        shadowView.layer.shadowColor = [UIColor colorWithWhite:0 alpha:0.4].CGColor;
        shadowView.layer.shouldRasterize = YES;
        shadowView.layer.shadowPath = [UIBezierPath bezierPathWithRect:shadowView.bounds].CGPath;
        self.panShadowView = shadowView;
    }
    self.panShadowView.frame = CGRectMake(0, 0, 1, self.view.imy_height);
    [self.view insertSubview:self.panShadowView atIndex:0];
    // 加截图
    UIView *superView = self.view.superview;
    self.panPresentView.frame = superView.bounds;
    [superView insertSubview:self.panPresentView atIndex:0];
    self.panPresentView.imy_left = SCREEN_WIDTH * -0.4;

    // 手势冲突处理：禁用标签页切换手势，防止与返回手势冲突
    if (self.tabContainerView && [self.tabContainerView respondsToSelector:@selector(setTabSwitchGestureEnabled:)]) {
        [self.tabContainerView setTabSwitchGestureEnabled:NO];
    }

    return YES;
}

- (void)handlePanDismissGesture:(UIPanGestureRecognizer *)panGesture {
    switch (panGesture.state) {
        case UIGestureRecognizerStateChanged: {
            // 获取偏移距离
            CGPoint translation = [panGesture translationInView:panGesture.view];
            [panGesture setTranslation:CGPointZero inView:panGesture.view];
            CGFloat x = self.view.imy_left + translation.x;
            if (x < 0) {
                x = 0;
            } else if (x > SCREEN_WIDTH) {
                x = SCREEN_WIDTH;
            }
            self.view.imy_left = x;
            CGFloat progress = MAX(0, MIN(1.0, x / SCREEN_WIDTH));
            self.panPresentView.imy_left = SCREEN_WIDTH * (-0.4 + 0.4 * progress);
        } break;
        case UIGestureRecognizerStateEnded:
        case UIGestureRecognizerStateCancelled:
        case UIGestureRecognizerStateFailed: {
            // 手势冲突处理：恢复标签页切换手势
            if (self.tabContainerView && [self.tabContainerView respondsToSelector:@selector(setTabSwitchGestureEnabled:)]) {
                [self.tabContainerView setTabSwitchGestureEnabled:YES];
            }

            // 动画过程中，不允许交互
            self.view.userInteractionEnabled = NO;
            if (self.view.imy_left > SCREEN_WIDTH * 0.25) {
                // 滑动超过 1/4 既算退出 (跟底层判断一致)
                [UIView animateWithDuration:0.2 animations:^{
                    self.view.imy_left = SCREEN_WIDTH;
                    self.panPresentView.imy_left = 0;
                } completion:^(BOOL finished) {
                    self.view.userInteractionEnabled = YES;
                    [self dismissViewControllerAnimated:NO completion:nil];
                }];
            } else {
                // 返回到原位，并移除追加的阴影
                [UIView animateWithDuration:0.2 animations:^{
                    self.view.imy_left = 0;
                    self.panPresentView.imy_left = SCREEN_WIDTH * -0.4;
                } completion:^(BOOL finished) {
                    self.view.userInteractionEnabled = YES;
                    [self.panPresentView removeFromSuperview];
                    [self.panShadowView removeFromSuperview];
                }];
            }
        } break;
        default: {
            break;
        }
    }
}

#pragma mark - Page 生命周期

- (void)onPageExitAction {
    // 收起键盘
    [self.searchBar.textField resignFirstResponder];
    // 页面退出动画
    if (self.isSearchHomePage && self.presentingViewController || self.fromCommunity) {
        UINavigationController * const navVC = self.navigationController;
        navVC.view.userInteractionEnabled = NO;
        
        if (self.befromBarFrame.length > 0) {
            // 有具体坐标系的动画
            UIWindow *window = self.view.window;
            UIView *animationBar = self.animationBar;
            
            CGRect endFrame = CGRectFromString(self.befromBarFrame);
            CGRect beginFrame = [self.view convertRect:animationBar.frame toView:window];
            
            animationBar.frame = beginFrame;
            [window addSubview:animationBar];
            
            [UIView animateWithDuration:0.15
                             animations:^{
                animationBar.frame = endFrame;
                if (self.presentingViewController) {
                    [self dismissViewControllerAnimated:YES completion:nil];
                } else if (self.navigationController && [self.navigationController.viewControllers containsObject:self]) {
                    [self imy_pop:NO];
                }
            } completion:^(BOOL finished) {
                imy_asyncMainBlock(^{
                    [animationBar removeFromSuperview];
                    navVC.view.userInteractionEnabled = YES;
                });
            }];
        } else {
            // 默认动画
            [UIView transitionWithView:navVC.view.window
                              duration:0.15
                               options:UIViewAnimationOptionTransitionCrossDissolve
                            animations:^{
                if (self.presentingViewController) {
                    [self dismissViewControllerAnimated:YES completion:nil];
                } else if (self.navigationController && [self.navigationController.viewControllers containsObject:self]) {
                    [self imy_pop:NO];
                }
            } completion:^(BOOL finished){
                navVC.view.userInteractionEnabled = YES;
            }];
        }
    } else {
        // 动画期间暂停用户交互
        UINavigationController * const navVC = self.navigationController;
        navVC.view.userInteractionEnabled = NO;
        [UIView transitionWithView:navVC.view
                          duration:0.1f
                           options:UIViewAnimationOptionTransitionCrossDissolve
                        animations:^{
            [navVC popViewControllerAnimated:NO];
        } completion:^(BOOL finished){
            navVC.view.userInteractionEnabled = YES;
        }];
    }
}

- (void)viewDidAppear:(BOOL)animated {
    BOOL isFirstShow = !self.isViewDidAppeared;
    [super viewDidAppear:animated];
    if (isFirstShow && !self.no_show_keyboard) {
        [self.searchBar.textField becomeFirstResponder];
    }
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    if (self.isSearchBarInputing) {
        [self.searchBar.textField becomeFirstResponder];
        self.isSearchBarInputing = NO;
    }

    // 如果收到了SearchBackgroundWordChangedNotification通知，在页面返回时聚焦搜索栏
    if (self.shouldFocusSearchBarOnReturn) {
        [self.searchBar.textField becomeFirstResponder];
        self.shouldFocusSearchBarOnReturn = NO; // 重置标记
    }

    // 手势冲突处理：确保标签页切换手势处于正确状态
    if (self.tabContainerView && [self.tabContainerView respondsToSelector:@selector(setTabSwitchGestureEnabled:)]) {
        [self.tabContainerView setTabSwitchGestureEnabled:YES];
    }
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    [self.searchBar.textField resignFirstResponder];
}

#pragma mark - webView's configue

- (NSInteger)current_tab {
    if (_current_tab <= 0) {
        _current_tab = 1;
    }
    return _current_tab;
}

#pragma mark - search's action

- (void)textFieldWithSearch:(UITextField *)sender {
    NSString *keyword = [self.searchBar.textField.text imy_trimString];
    if (imy_isNotBlankString(keyword)) {
        [self.searchBar.textField resignFirstResponder];
        self.keyword = keyword;
        [self searchWithKeyWord:keyword wordsType:SYSearchWordTypeUserInput location:@"搜索栏"];
        
        // 用户手动输入搜索埋点
        NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
        gaParams[@"func"] = @(23);
        gaParams[@"pos_id"] = @(self.pos_id);
        gaParams[@"location"] = @"搜索栏";
        gaParams[@"location_index"] = @0;
        gaParams[@"key"] = keyword;
        gaParams[@"words_type"] = @(SYSearchWordTypeUserInput);
        gaParams[@"searcy_key"] = self.search_key;
        gaParams[@"info_type"] = @(self.info_type);
        [IMYGAEventHelper postWithPath:@"search-static" params:gaParams headers:nil completed:nil];
    } else {
        if (self.isRecommendWord && ![self isDefaultPlaceholder:self.searchBar.textField.placeholder]) {
            [self.searchBar.textField resignFirstResponder];
            self.keyword = self.searchBar.textField.text;
            if ([IMYPublicAppHelper shareAppHelper].userMode == IMYVKUserModePregnancy || self.isRecommendWordAndSearch) {
                NSString *text = [self.searchBar.textField.placeholder imy_trimString];
                self.keyword = text;
                
                // 8.93.0版本：从searchPlaceholder字典中获取scheme_uri
                NSString *schemeUri = [self getSchemeURIFromSearchPlaceholder];
                // 8.93.0版本：将searchPlaceholder字典作为sourceModel传入历史记录
                IMYNASearchHistoryModel *historyModel = nil;
                if ([self.searchPlaceholder isKindOfClass:[NSDictionary class]]) {
                    historyModel = [IMYNASearchHistoryModel modelWithKeyword:text sourceModel:self.searchPlaceholder];
                }
                if ([schemeUri isKindOfClass:[NSString class]] && schemeUri.length > 0) {
                    // 有scheme_uri，执行跳转
                    [[IMYURIManager shareURIManager] runActionWithString:schemeUri];
                    // 添加到搜索历史
                    [[NSNotificationCenter defaultCenter] postNotificationName:IMYSearchH5KeywordChangedNotification object:historyModel];
                    // 跳转埋点
                    NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
                    gaParams[@"func"] = @(23);
                    gaParams[@"pos_id"] = @(self.pos_id);
                    gaParams[@"location"] = @"搜索栏";
                    gaParams[@"location_index"] = @0;
                    gaParams[@"key"] = text;
                    gaParams[@"words_type"] = @(SYSearchWordTypeRecommend);
                    gaParams[@"searcy_key"] = self.search_key;
                    gaParams[@"info_type"] = @(self.info_type);
                    gaParams[@"scheme_uri"] = schemeUri; // 记录跳转URI
                    [IMYGAEventHelper postWithPath:@"search-static" params:gaParams headers:nil completed:nil];
                    
                    // 底纹词点击上报：新增一条埋点
                    [self postShadingClickForKeyword:text sourceModel:self.searchPlaceholder];
                } else {
                    // 无scheme_uri，按推荐词处理，执行搜索
                    // 用户搜索-推荐词埋点
                    NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
                    gaParams[@"func"] = @(23);
                    gaParams[@"pos_id"] = @(self.pos_id);
                    gaParams[@"location"] = @"搜索栏";
                    gaParams[@"location_index"] = @0;
                    gaParams[@"key"] = text;
                    gaParams[@"words_type"] = @(SYSearchWordTypeRecommend);
                    gaParams[@"searcy_key"] = self.search_key;
                    gaParams[@"info_type"] = @(self.info_type);
                    [IMYGAEventHelper postWithPath:@"search-static" params:gaParams headers:nil completed:nil];
                    
                    // 底纹词点击上报：新增一条埋点
                    [self postShadingClickForKeyword:text sourceModel:self.searchPlaceholder];
                    
                    [self searchWithKeyWord:text wordsType:SYSearchWordTypeRecommend location:@"搜索栏" historyModel:historyModel];
                }
                return;
            }
        }
        self.searchBar.textField.text = nil;
        [UIWindow imy_showTextHUD:self.placeholderText];
    }
}

- (BOOL)isDefaultPlaceholder:(NSString *)placeholder {
    NSMutableArray *defaultArray = [[NSMutableArray alloc] initWithObjects:self.placeholderText, nil];
    if (imy_isNotBlankString(self.defaultSearchPlaceholder)) {
        [defaultArray addObject:self.defaultSearchPlaceholder];
    }
    return [defaultArray containsObject:placeholder];
}

- (void)onResultPageWithSearchAction:(NSNotification *)notify {
    id notifyObject = notify.object;
    NSString *keyword = nil;
    
    if ([notifyObject isKindOfClass:[IMYNASearchHistoryModel class]]) {
        // 历史记录模型
        IMYNASearchHistoryModel *historyModel = (IMYNASearchHistoryModel *)notifyObject;
        keyword = historyModel.keyword;
        [self refreshHistoryViewWithHistoryModel:historyModel];
    }
}

- (void)onTextFieldResultShouldClearAction:(NSNotification *)notify {
    // 结果页清空的搜索词
    NSArray *VCs = self.navigationController.viewControllers;
    NSUInteger index = [VCs indexOfObject:self];
    if (index + 1 >= VCs.count - 1) {
        self.isSearchBarInputing = [notify.object boolValue];
        self.keyword = @"";
        self.searchBar.textField.text = @"";
        [self.associateVC clearData];
        self.associateVC.view.hidden = YES;
        self.lastTextFiledText = nil;
    }
    if (self.originalIconType > 0) {
        [self.searchBar updateIconTagWithType:self.originalIconType];
    }
}

- (void)onSearchBackgroundWordChanged:(NSNotification *)notify {
    // 处理搜索背景词变更通知
    NSDictionary *userInfo = notify.object;
    if ([userInfo isKindOfClass:[NSDictionary class]]) {
        NSString *searchPlaceholder = userInfo[@"searchPlaceholder"];
        if ([searchPlaceholder isKindOfClass:[NSString class]] && searchPlaceholder.length > 0) {
            // 更新搜索栏的placeholder为接收到的keyword
            self.searchBar.textField.placeholder = searchPlaceholder;

            // 设置标记，在页面返回时需要聚焦搜索栏
            self.shouldFocusSearchBarOnReturn = YES;
            
            NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
            gaParams[@"func"] = @(22);
            gaParams[@"pos_id"] = @(self.pos_id);
            gaParams[@"words"] = searchPlaceholder;
            gaParams[@"words_type"] = @(1);
            gaParams[@"shading_location"] = @(2);
            
            [IMYGAEventHelper postWithPath:@"search-static" params:gaParams headers:nil completed:nil];
        }
    }
}

/// UIControlEventEditingChanged
- (void)textFieldDidTextChanged:(UITextField *)textField {
    NSString * const inputText = textField.text ?: @"";
    if (![self.lastTextFiledText isEqualToString:inputText]) {
        self.lastTextFiledText = inputText;
        @weakify(self);
        [NSObject imy_asyncBlock:^{
            @strongify(self);
            if (imy_isNotBlankString(inputText)) {
                self.associateVC.view.hidden = NO;
                [self.associateVC reqAssociateWords:inputText];
            } else {
                [self.associateVC clearData];
                self.associateVC.view.hidden = YES;
            }
        } onQueue:dispatch_get_main_queue() afterSecond:0.1 forKey:kIMYSearchHomeOnAssociateChangeKey];
    }
    
    // 处理图标显示逻辑
    if (inputText.length > 0) {
        // 只在第一次输入时保存原始图标类型（使用oldTextLength判断）
        NSString *oldText = self.lastTextFiledText ?: @"";
        if (oldText.length == 0) {
            self.originalIconType = [self getIconTypeFromSearchPlaceholder];
        }
        // 有输入时清空图标
        [self.searchBar updateIconTagWithType:0];
    } else {
        // 输入为空时恢复原始图标（如果有的话）
        if (self.originalIconType > 0) {
            [self.searchBar updateIconTagWithType:self.originalIconType];
        }
    }
    
    if (!self.isExposuredClearButton && inputText.length > 0) {
        // 清空按钮曝光，一个生命周期只曝光一次
        self.isExposuredClearButton = YES;
        NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
        gaParams[@"func"] = @(32);
        gaParams[@"pos_id"] = @(self.pos_id);
        gaParams[@"info_type"] = @(self.info_type);
        [IMYGAEventHelper postWithPath:@"search-static" params:gaParams headers:nil completed:nil];
    }
    // 无内容情况下，重置清空按钮曝光
    if (!inputText.length) {
        self.isExposuredClearButton = NO;
    }
}

- (void)textFieldEditingDidEnd:(UITextField *)textField {
    // 输入框无焦点，进行曝光
    
    // 如果用户结束输入且输入框为空，恢复原始图标（如果有的话）
    NSString *currentText = textField.text ?: @"";
    if (currentText.length == 0 && self.originalIconType > 0) {
        [self.searchBar updateIconTagWithType:self.originalIconType];
    }
}

- (void)searchWithKeyWord:(NSString *)keyword wordsType:(SYSearchWordType)wordsType location:(NSString *)location {
    IMYNASearchHistoryModel *historyModel = [IMYNASearchHistoryModel modelWithKeyword:keyword sourceModel:nil];
    [self searchWithKeyWord:keyword wordsType:wordsType location:location historyModel:historyModel];
}

- (void)searchWithKeyWord:(NSString *)keyword wordsType:(SYSearchWordType)wordsType location:(NSString *)location historyModel:(IMYNASearchHistoryModel *)historyModel {
    if (!wordsType) {
        wordsType = SYSearchWordTypeUserInput;
    }
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"keyword"] = keyword ?: @"";
    params[@"bi_source"] = @(self.bi_source);
    params[@"type"] = @(SYSearchResultTypeUnknown);
    params[@"trendStyle"] = @(YES);
    params[@"from"] = @(self.from);
    params[@"current_tab"] = @(self.current_tab);
    params[@"words_type"] = @(wordsType);
    params[@"pos_id"] = @(self.pos_id);
    params[@"index"] = @(0);
    params[@"biType"] = @(SYSearchClickTypeTouch);
    params[@"animated"] = @(NO);
    params[@"location"] = location ?: @"";
    params[@"searchWord"] = self.searchBar.textField.text ?: @"";
    params[@"search_key"] = self.search_key;
    params[@"info_type"] = @(self.info_type);
    if (self.fromCommunity) {
        NSNumber *value = (NSNumber *)self.fromURI.params[@"search_home_type"];
        params[@"search_home_type"] = value;
    }
    
    if(self.forum_id > 0){
        params[@"forum_id"] = @(self.forum_id);
        params[@"new_circle"] = @(1);
    }
    
    // 跳转到搜索结果页
    [[IMYURIManager shareURIManager] runActionWithPath:@"circles/searchresult" params:params info:nil];
    
    // 清空当前输入框
    self.associateVC.view.hidden = YES;
    self.searchBar.textField.text = @"";
    [[NSNotificationCenter defaultCenter] postNotificationName:IMYSearchH5KeywordChangedNotification object:historyModel];
}

- (void)refreshHistoryViewWithHistoryModel:(IMYNASearchHistoryModel *)historyModel {
    NSArray<IMYNASearchHistoryModel *> *historyModels = self.historyView.historyModels;
    
    // 检查是否已经在第一位，使用关键词进行判断
    BOOL isFirstItemSameKeyword = historyModels.firstObject && [historyModels.firstObject isEqual:historyModel];
    
    if (!isFirstItemSameKeyword) {
        // 插到历史记录第一位
        NSMutableArray *mutableArray = [NSMutableArray array];
        
        // 直接添加传入的历史记录模型到第一位
        [mutableArray addObject:historyModel];
        
        // 添加其他已存在的历史记录模型，排除相同关键词的模型（实现覆盖效果）
        for (IMYNASearchHistoryModel *existingModel in historyModels) {
            // 只保留关键词不同的模型，实现相同keyword的替换逻辑
            if (![existingModel.keyword isEqualToString:historyModel.keyword]) {
                [mutableArray addObject:existingModel];
            }
        }
        
        // 统一保存模型数据到本地
        [IMYNASearchHomeVC saveHistoryModels:mutableArray];
        
        // 迟点更新UI，避免首页闪烁
        @weakify(self);
        imy_asyncMainBlock(0.5, ^{
            @strongify(self);
            // 使用模型数组方式更新UI
            [self.historyView setupWithHistoryModels:mutableArray animated:NO];
        });
    }
}

#pragma mark - getter & setter

- (SYHomeSearchAssociateVC *)associateVC {
    if (!_associateVC) {
        _associateVC = [[SYHomeSearchAssociateVC alloc] init];
        _associateVC.pos_id = self.pos_id;
        _associateVC.from = self.from;
        _associateVC.search_key = self.search_key;
        _associateVC.info_type = self.info_type;
        [self addChildViewController:_associateVC];
        
        @weakify(self);
        _associateVC.scrollBlock = ^{
            @strongify(self);
            [self.searchBar.textField resignFirstResponder];
        };
        
        // 根据AB实验值决定使用何种联想词回调
        if (self.hiszdExperimentValue == 0) {
            // 实验值为0：只使用普通的字符串回调
            _associateVC.seletedCellBlock = ^(NSString *keyword, NSUInteger index) {
                @strongify(self);
                self.keyword = keyword;
                
                NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
                gaParams[@"func"] = @(IMYSearchEventTrackFuncTypeAssociateClick); // 23
                gaParams[@"pos_id"] = @(self.pos_id);
                gaParams[@"key"] = keyword;
                gaParams[@"words_type"] = @(IMYSearchWordsTypeLenovo); // 3
                gaParams[@"location_index"] = @(index + 1);
                gaParams[@"searcy_key"] = self.search_key;
                gaParams[@"info_type"] = @(self.info_type);
                gaParams[@"suggest_type"] = @1;
                [IMYGAEventHelper postWithPath:@"search-static" params:gaParams headers:nil completed:nil];
                [self searchWithKeyWord:keyword wordsType:SYSearchWordTypeAssociate location:@"联想词"];
            };
        } else {
            // 实验值为1或2：优先使用历史模型回调
            _associateVC.seletedToolButtonWithHistoryModelBlock = ^(NSString *keyword, NSUInteger index, IMYNASearchHistoryModel *historyModel) {
                @strongify(self);
                self.keyword = keyword;
                NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
                gaParams[@"func"] = @(IMYSearchEventTrackFuncTypeAssociateClick); // 23
                gaParams[@"pos_id"] = @(self.pos_id);
                gaParams[@"key"] = keyword;
                gaParams[@"words_type"] = @(IMYSearchWordsTypeLenovo); // 3
                gaParams[@"location_index"] = @(index + 1);
                gaParams[@"searcy_key"] = self.search_key;
                gaParams[@"info_type"] = @(self.info_type);
                gaParams[@"suggest_type"] = @2;
                gaParams[@"location"] = @"联想词";
                [IMYGAEventHelper postWithPath:@"search-static" params:gaParams headers:nil completed:nil];
                [[NSNotificationCenter defaultCenter] postNotificationName:IMYSearchH5KeywordChangedNotification object:historyModel];
            };
            _associateVC.seletedCellWithHistoryModelBlock = ^(NSString *keyword, NSUInteger index, IMYNASearchHistoryModel *historyModel) {
                @strongify(self);
                self.keyword = keyword;
                NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
                gaParams[@"func"] = @(IMYSearchEventTrackFuncTypeAssociateClick); // 23
                gaParams[@"pos_id"] = @(self.pos_id);
                gaParams[@"key"] = keyword;
                gaParams[@"words_type"] = @(IMYSearchWordsTypeLenovo); // 3
                gaParams[@"location_index"] = @(index + 1);
                gaParams[@"searcy_key"] = self.search_key;
                gaParams[@"info_type"] = @(self.info_type);
                gaParams[@"suggest_type"] = @1;
                gaParams[@"location"] = @"联想词";
                [IMYGAEventHelper postWithPath:@"search-static" params:gaParams headers:nil completed:nil];
                // 创建新的基础历史记录模型，不包含sourceModel，覆盖直达样式
                IMYNASearchHistoryModel *basicHistoryModel = [IMYNASearchHistoryModel modelWithKeyword:historyModel.keyword];

                [self searchWithKeyWord:keyword wordsType:SYSearchWordTypeAssociate location:@"联想词" historyModel:basicHistoryModel];
                // 直接传递基础历史记录模型，实现覆盖效果
                [[NSNotificationCenter defaultCenter] postNotificationName:IMYSearchH5KeywordChangedNotification object:basicHistoryModel];
            };
            
            // 保持向后兼容的普通回调
            _associateVC.seletedCellBlock = ^(NSString *keyword, NSUInteger index) {
                @strongify(self);
                self.keyword = keyword;
                
                NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
                gaParams[@"func"] = @(IMYSearchEventTrackFuncTypeAssociateClick); // 23
                gaParams[@"pos_id"] = @(self.pos_id);
                gaParams[@"key"] = keyword;
                gaParams[@"words_type"] = @(IMYSearchWordsTypeLenovo); // 3
                gaParams[@"location_index"] = @(index + 1);
                gaParams[@"searcy_key"] = self.search_key;
                gaParams[@"info_type"] = @(self.info_type);
                gaParams[@"suggest_type"] = @1;
                [IMYGAEventHelper postWithPath:@"search-static" params:gaParams headers:nil completed:nil];
                [self searchWithKeyWord:keyword wordsType:SYSearchWordTypeAssociate location:@"联想词"]; //SYSearchWordTypeAssociate = 3
            };
        }
        
        [self.view addSubview:_associateVC.view];
        _associateVC.view.frame = self.mainScrollView.frame;
        _associateVC.view.hidden = YES;
    }
    return _associateVC;
}

- (IMYNAInputSearchBar *)searchBar {
    if (!_searchBar) {
        _searchBar = [IMYNAInputSearchBar new];
        
        // 根据缓存的实验值配置搜索栏样式
        if (self.searchBarStyleValue == 1 || self.searchBarStyleValue == 2) {
            // 实验组1和2：使用新的样式配置
            [_searchBar setupWithExperimentGroup:self.searchBarStyleValue];
        } else if (!self.usingNewSearchStyle) {
            // 老样式：隐藏左按钮
            [_searchBar usingHideLeftButtonStyle];
        }
        // 其他值且usingNewSearchStyle为YES时，使用默认样式
        _searchBar.imy_width = SCREEN_WIDTH;
        _searchBar.textField.text = self.keyword;
        NSString *placeholder = self.placeholderText;
        
        // 从searchPlaceholder字典中提取keyword用于显示
        NSString *searchPlaceholderKeyword = [self getKeywordFromSearchPlaceholder];
        
        if (searchPlaceholderKeyword.length > 0) {
            //
            // 使用外部的推荐词，当做 placeholder
            placeholder = searchPlaceholderKeyword;
            // 无内容 键盘也可以点击搜索
            if (self.isRecommendWord) {
                _searchBar.textField.enablesReturnKeyAutomatically = NO;
            }
        }
        _searchBar.textField.placeholder = placeholder;

        NSInteger iconType = [self getIconTypeFromSearchPlaceholder];
        if (iconType > 0) {
            // 确保在设置placeholder后立即创建图标，避免延迟显示
            [_searchBar updateIconTagWithType:iconType];
            // 初始化时保存原始图标类型
            self.originalIconType = iconType;
        }
        
        [_searchBar.textField addTarget:self action:@selector(textFieldWithSearch:) forControlEvents:UIControlEventEditingDidEndOnExit];
        [_searchBar.textField addTarget:self action:@selector(textFieldDidTextChanged:) forControlEvents:UIControlEventEditingChanged];
        [_searchBar.textField addTarget:self action:@selector(textFieldEditingDidEnd:) forControlEvents:UIControlEventEditingDidEnd];
        @weakify(self);
        _searchBar.onTextFieldClearBlock = ^(UITextField *textField) {
            @strongify(self);
            // 清空按钮点击
            NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
            gaParams[@"func"] = @(33);
            gaParams[@"pos_id"] = @(self.pos_id);
            gaParams[@"info_type"] = @(self.info_type);
            [IMYGAEventHelper postWithPath:@"search-static" params:gaParams headers:nil completed:nil];
            // 清空按钮曝光，一个生命周期只曝光一次
            if (!self.isExposuredClearButton) {
                self.isExposuredClearButton = YES;
                NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
                gaParams[@"func"] = @(32);
                gaParams[@"pos_id"] = @(self.pos_id);
                gaParams[@"info_type"] = @(self.info_type);
                [IMYGAEventHelper postWithPath:@"search-static" params:gaParams headers:nil completed:nil];
            }
            // 重置清空按钮的曝光判断
            self.isExposuredClearButton = NO;
            
            // 清空输入框后恢复原始图标（如果有的话）
            if (self.originalIconType > 0) {
                [self.searchBar updateIconTagWithType:self.originalIconType];
            }
        };
        [_searchBar setOnRightButtonClick:^{
            @strongify(self);
            if (self.searchBarStyleValue == 1 || self.searchBarStyleValue == 2) {
                // 实验组1和2：右按钮是搜索
                [self textFieldWithSearch:self.searchBar.textField];
            } else if (self.usingNewSearchStyle) {
                // 其他新样式：右按钮是搜索
                [self textFieldWithSearch:self.searchBar.textField];
            } else {
                // 老样式：右按钮是取消
                [self onPageExitAction];
            }
        }];
        [_searchBar setOnLeftButtonClick:^{
            @strongify(self);
            [self onPageExitAction];
        }];
        if (self.showAssociateWithKey && self.keyword.length) {
            /// 如果需要马上显示联想词的
            [self textFieldDidTextChanged:_searchBar.textField];
        }
    }
    return _searchBar;
}

- (NSString *)placeholderText {
    return IMYString(@"请输入关键字");
}

- (IMYAdSignal *)adShowSignal {
    if (!_adShowSignal) {
        _adShowSignal = [IMYAdSignal adSignalWithPage:IMYADPageSearchBar userInfo:nil];
    }
    return _adShowSignal;
}

- (IMYAdSignal *)adClickSignal {
    if (!_adClickSignal) {
        _adClickSignal = [IMYAdSignal adSignalWithPage:IMYADPageSearchBar userInfo:nil];
    }
    return _adClickSignal;
}

#pragma mark - 负反馈处理

- (void)onGuessYouFeedbackButtonPressed {
    [self.view endEditing:YES];
    // 获取AB实验值
    IMYABTestExperiment *guessLayoutExp = [[IMYABTestManager sharedInstance] experimentForKey:@"guessdislike"];
    NSInteger guessLayoutValue = [guessLayoutExp.vars integerForKey:@"guess"];
    
    // 根据实验值确定反馈scheme
    IMYNativeFeedbackScheme scheme = IMYNativeFeedbackScheme_12_6; // 默认scheme
    if (guessLayoutValue == 2) {
        scheme = IMYNativeFeedbackScheme_6_6;  // 实验组2：3行2列
    } else if (guessLayoutValue == 3) {
        scheme = IMYNativeFeedbackScheme_8_6;  // 实验组3：4行2列
    } else {
        scheme = IMYNativeFeedbackScheme_12_6; // 实验组1：当前样式（原始样式）
    }
    
    // 提取当前界面显示的联想词（与界面展示保持一致）
    NSArray<NSString *> *feedbackWords = [self.guessYouView getCurrentDisplayedKeywords];
    
    // 如果没有词条，不显示反馈界面
    if (feedbackWords.count == 0) {
        return;
    }
    
    // 直接显示反馈界面（使用预加载的数据）
    @weakify(self);
    [[IMYNativeFeedbackManager sharedInstance] showFeedbackCardDirectlyWithWords:feedbackWords
                                                                           scheme:scheme
                                                                       completion:^(BOOL success, IMYSurveyModel *surveyModel) {
        @strongify(self);
        if (!success) {
            // 展示失败时隐藏操作按钮和分割线
            [self.guessYouView hideActionButtonAndSeparator];
        }
    }];
}

/// 预加载反馈数据
- (void)preloadFeedbackData {
    @weakify(self);
    [[IMYNativeFeedbackManager sharedInstance] preloadFeedbackDataWithCompletion:^(BOOL success) {
        @strongify(self);
        if (!success) {
            // 预加载失败，隐藏反馈按钮
            [self.guessYouView hideActionButtonAndSeparator];
        }
    }];
}

#pragma mark - GAEvent

// 入口页点击搜索框埋点
- (void)postEntranceClickSearchBarEvent {
    NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
    gaParams[@"func"] = @(1);
    gaParams[@"pos_id"] = @(self.pos_id);
    gaParams[@"searcy_key"] = self.search_key;
    gaParams[@"info_type"] = @(self.info_type);
    [IMYGAEventHelper postWithPath:@"search-static" params:gaParams headers:nil completed:nil];
}

/// 获取历史记录类型：1为普通词，2为小工具直达
- (NSInteger)getSuggestTypeForHistoryModel:(IMYNASearchHistoryModel *)historyModel {
    if (!historyModel || !historyModel.sourceModel) {
        return 1; // 默认为普通词
    }
    
    NSDictionary *sourceModel = historyModel.sourceModel;
    NSInteger type = [sourceModel[@"type"] integerValue];
    
    // type == 2 且有 tool 信息表示小工具直达
    if (type == 2 && sourceModel[@"tool"] != nil) {
        return 2;
    }
    // 8.95：高亮词类型（新类型4）
    if ([historyModel isHighlightWordType]) {
        return 4;
    }
    
    return 1; // 普通词
}

/// 获取历史记录关键词数组对应的suggest_types数组
- (NSArray *)getSuggestTypesForHistoryKeys:(NSArray<NSString *> *)historyKeys {
    NSMutableArray *suggestTypes = [NSMutableArray array];
    
    // 获取当前历史记录模型数组
    NSArray<IMYNASearchHistoryModel *> *historyModels = self.historyView.historyModels;
    
    for (NSString *keyword in historyKeys) {
        NSInteger suggestType = 1; // 默认为普通词
        
        // 查找对应的历史记录模型
        for (IMYNASearchHistoryModel *model in historyModels) {
            if ([model.keyword isEqualToString:keyword]) {
                // 8.93.0版本：检查是否为轮播词类型
                if ([model isCarouselWordType]) {
                    suggestType = 3; // 轮播词类型
                } else {
                    suggestType = [self getSuggestTypeForHistoryModel:model];
                }
                break;
            }
        }
        
        [suggestTypes addObject:@(suggestType)];
    }
    
    return [suggestTypes copy];
}

- (void)onExposuredWithHistoryKey:(NSString *)keyword {
    // 批量曝光
    if (![self.historyExposuredKeys containsObject:keyword]) {
        [self.historyExposuredKeys addObject:keyword];
    }
    [NSObject imy_asyncBlock:^{
        // 曝光keys
        NSArray *showedKeys = [self.historyExposuredKeys copy];
        [self.historyExposuredKeys removeAllObjects];
        // 保证排序
        NSMutableArray *origKeys = [self.historyView.historyKeys mutableCopy];
        NSMutableArray *needDeleteKeys = [origKeys mutableCopy];
        [needDeleteKeys removeObjectsInArray:showedKeys];
        [origKeys removeObjectsInArray:needDeleteKeys];
        showedKeys = [origKeys copy];
        // 埋点
        NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
        gaParams[@"func"] = @(22);
        gaParams[@"pos_id"] = @(self.pos_id);
        gaParams[@"location"] = @"搜索历史";
        gaParams[@"location_index"] = @0;
        gaParams[@"words"] = showedKeys;
        gaParams[@"words_type"] = @(SYSearchWordTypeKeyword);
        gaParams[@"suggest_types"] = [self getSuggestTypesForHistoryKeys:showedKeys];
        gaParams[@"searcy_key"] = self.search_key;
        gaParams[@"info_type"] = @(self.info_type);
        [IMYGAEventHelper postWithPath:@"search-static" params:gaParams headers:nil completed:nil];
    } onQueue:dispatch_get_main_queue() afterSecond:1 forKey:@"na.search.history.exposure"];
}

- (void)onClickedWithHistoryKey:(NSString *)keyword {
    // 开始搜索
    [self searchWithKeyWord:keyword wordsType:SYSearchWordTypeKeyword location:@"搜索历史"];
    // 埋点
    NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
    gaParams[@"func"] = @(23);
    gaParams[@"pos_id"] = @(self.pos_id);
    gaParams[@"location"] = @"搜索历史";
    // 获取真实位置
    NSUInteger index = [self.historyView.historyKeys indexOfObject:keyword];
    gaParams[@"location_index"] = @(index + 1);
    gaParams[@"key"] = keyword;
    gaParams[@"words_type"] = @(SYSearchWordTypeKeyword);
    
    // 获取对应的历史记录模型以确定suggest_type
    NSInteger suggestType = 1; // 默认为普通词
    NSArray<IMYNASearchHistoryModel *> *historyModels = self.historyView.historyModels;
    for (IMYNASearchHistoryModel *model in historyModels) {
        if ([model.keyword isEqualToString:keyword]) {
            // 8.93.0版本：检查是否为轮播词类型
            if ([model isCarouselWordType]) {
                suggestType = 3; // 轮播词类型
            } else {
                suggestType = [self getSuggestTypeForHistoryModel:model];
            }
            break;
        }
    }
    gaParams[@"suggest_type"] = @(suggestType);
    
    gaParams[@"searcy_key"] = self.search_key;
    gaParams[@"info_type"] = @(self.info_type);
    [IMYGAEventHelper postWithPath:@"search-static" params:gaParams headers:nil completed:nil];
}

/// 判断是否应该直接跳转（直达小工具且AB实验值>=1）
- (BOOL)shouldDirectJumpForHistoryModel:(IMYNASearchHistoryModel *)historyModel {
    if (!historyModel || !historyModel.sourceModel) {
        return NO;
    }
    
    // 获取AB实验值
    if (self.hiszdExperimentValue < 1) {
        return NO;
    }
    
    // 检查sourceModel是否为直达小工具类型 (type == 2)
    NSDictionary *sourceModel = historyModel.sourceModel;
    NSInteger type = [sourceModel[@"type"] integerValue];
    
    return (type == 2 && sourceModel[@"tool"] != nil);
}

- (void)onClickedWithHistoryModel:(IMYNASearchHistoryModel *)historyModel {
    // 8.93.0版本：检查历史记录sourceModel中的scheme_uri
    NSString *schemeUri = nil;
    NSDictionary *sourceModel = historyModel.sourceModel;
    if ([sourceModel isKindOfClass:[NSDictionary class]]) {
        schemeUri = sourceModel[@"scheme_uri"];
    }
    // 埋点
    NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
    gaParams[@"func"] = @(23);
    gaParams[@"pos_id"] = @(self.pos_id);
    gaParams[@"location"] = @"搜索历史";
    // 获取真实位置
    NSUInteger index = [self.historyView.historyKeys indexOfObject:historyModel.keyword];
    gaParams[@"location_index"] = @(index + 1);
    gaParams[@"key"] = historyModel.keyword;
    gaParams[@"words_type"] = @(SYSearchWordTypeKeyword);
    
    // 获取对应的历史记录模型以确定suggest_type
    NSInteger suggestType = 1; // 默认为普通词
    // 8.93.0版本：检查是否为轮播词类型
    if ([historyModel isCarouselWordType]) {
        suggestType = 3; // 轮播词类型
    } else {
        suggestType = [self getSuggestTypeForHistoryModel:historyModel];
    }
    gaParams[@"suggest_type"] = @(suggestType);
    
    gaParams[@"searcy_key"] = self.search_key;
    gaParams[@"info_type"] = @(self.info_type);
    // 如果有scheme_uri，并且不是放大镜的模式，执行跳转
    if ([schemeUri isKindOfClass:[NSString class]] && schemeUri.length > 0) {
        // 执行scheme_uri跳转
        [[IMYURIManager shareURIManager] runActionWithString:schemeUri];
        // 更新历史记录使用次数
        [[NSNotificationCenter defaultCenter] postNotificationName:IMYSearchH5KeywordChangedNotification object:historyModel];
        [IMYGAEventHelper postWithPath:@"search-static" params:gaParams headers:nil completed:nil];
        return;
    }
    
    // AB实验值为0时的兼容处理：使用简化的搜索逻辑
    if (self.hiszdExperimentValue == 0) {
        // 开始搜索
        [self searchWithKeyWord:historyModel.keyword wordsType:SYSearchWordTypeKeyword location:@"搜索历史" historyModel:historyModel];
        [IMYGAEventHelper postWithPath:@"search-static" params:gaParams headers:nil completed:nil];
        return;
    }
    
    // AB实验值为1或2时的增强逻辑
    // 在外部判断AB实验组，决定是直达跳转还是搜索
    if ([self shouldDirectJumpForHistoryModel:historyModel]) {
        // 直达小工具跳转
        [self onDirectToolJumpWithHistoryModel:historyModel];
        [[NSNotificationCenter defaultCenter] postNotificationName:IMYSearchH5KeywordChangedNotification object:historyModel];
    } else {
        // 普通搜索逻辑 - 上报埋点
        NSUInteger index = [self.historyView.historyKeys indexOfObject:historyModel.keyword];
        NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
        gaParams[@"func"] = @(23);
        gaParams[@"pos_id"] = @(self.pos_id);
        gaParams[@"location"] = @"搜索历史";
        gaParams[@"location_index"] = @(index + 1);
        gaParams[@"key"] = historyModel.keyword;
        gaParams[@"words_type"] = @(SYSearchWordTypeKeyword);
        gaParams[@"suggest_type"] = @1; // 普通词
        gaParams[@"searcy_key"] = self.search_key;
        gaParams[@"info_type"] = @(self.info_type);
        [IMYGAEventHelper postWithPath:@"search-static" params:gaParams headers:nil completed:nil];

        // 保持原有的历史记录模型，确保scheme_uri信息不丢失
        // 执行搜索
        [self searchWithKeyWord:historyModel.keyword wordsType:SYSearchWordTypeKeyword location:@"搜索历史" historyModel:historyModel];
    }
}

/// 处理直达小工具跳转（包含埋点）
- (void)onDirectToolJumpWithHistoryModel:(IMYNASearchHistoryModel *)historyModel {
    NSDictionary *sourceModel = historyModel.sourceModel;
    NSDictionary *tool = sourceModel[@"tool"];
    
    if (!tool || !tool[@"uri"]) {
        // 如果没有工具信息，降级为普通搜索
        [self onClickedWithHistoryModel:historyModel];
        return;
    }
    
    // 获取在历史记录中的位置
    NSUInteger index = [self.historyView.historyKeys indexOfObject:historyModel.keyword];
    
    // 埋点逻辑（参考SYHomeSearchAssociateVC中的onClickToolModel方法）
    NSString *tool_title = historyModel.keyword ?: @"";
    NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
    gaParams[@"func"] = @(23); // IMYSearchEventTrackFuncTypeAssociateClick
    gaParams[@"pos_id"] = @(self.pos_id);
    gaParams[@"key"] = tool_title;
    gaParams[@"words_type"] = @(3); // IMYSearchWordsTypeLenovo
    gaParams[@"location"] = @"搜索历史";
    gaParams[@"location_index"] = @(index + 1);
    gaParams[@"searcy_key"] = self.search_key;
    gaParams[@"info_type"] = @(self.info_type);
    gaParams[@"suggest_type"] = @2; // 小工具类型
    [IMYGAEventHelper postWithPath:@"search-static" params:gaParams headers:nil completed:nil];
    
    // 执行直达跳转
    NSString *toolUri = tool[@"uri"];
    if (toolUri.length > 0) {
        [[IMYURIManager shareURIManager] runActionWithString:toolUri];
    }
}

- (void)onExposuredWithGuessYouKey:(IMYNASearchGuessYouKeyModel *)keyModel {
    // 广告会全部曝光，内部自己判断是否广告model
    if (self.adShowSignal) {
        NSMutableDictionary *adInfo = [NSMutableDictionary dictionary];
        [adInfo setValue:@(keyModel.ad_model) forKey:@"isAd"];
        [adInfo setValue:keyModel.keyword forKey:@"content"];
        [adInfo setValue:@(6702) forKey:@"position"];
        [adInfo setValue:@(keyModel.index) forKey:@"index"];
        [adInfo setValue:self.search_key forKey:@"search_key"];
        [self.adShowSignal sendData:adInfo];
    }
    // 业务model自带的曝光监测
    if (!keyModel.ad_model && keyModel.show_ping.length > 0) {
        [[IMYServerRequest getPath:keyModel.show_ping host:nil params:nil headers:nil] subscribeCompleted:^{
            // OK
        }];
    }
    // 批量曝光
    if (![self.guessYouExposuredKeys containsObject:keyModel.keyword]) {
        [self.guessYouExposuredKeys addObject:keyModel.keyword];
    }
    [NSObject imy_asyncBlock:^{
        // 曝光keys
        NSArray *showedKeys = [self.guessYouExposuredKeys copy];
        [self.guessYouExposuredKeys removeAllObjects];
        
        // 保证排序
        NSMutableArray *origKeys = [[self.guessYouView.guessKeys map:^id(IMYNASearchGuessYouKeyModel *element) {
            return element.keyword;
        }] mutableCopy];
        NSMutableArray *needDeleteKeys = [origKeys mutableCopy];
        [needDeleteKeys removeObjectsInArray:showedKeys];
        [origKeys removeObjectsInArray:needDeleteKeys];
        showedKeys = [origKeys copy];
        
        // 埋点
        NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
        gaParams[@"func"] = @(22);
        gaParams[@"pos_id"] = @(self.pos_id);
        gaParams[@"location"] = @"猜你想搜";
        gaParams[@"location_index"] = @0;
        gaParams[@"words"] = showedKeys;
        gaParams[@"words_type"] = @(SYSearchWordTypeKeyword);
        gaParams[@"searcy_key"] = self.search_key;
        gaParams[@"info_type"] = @(self.info_type);
        [IMYGAEventHelper postWithPath:@"search-static" params:gaParams headers:nil completed:nil];
    } onQueue:dispatch_get_main_queue() afterSecond:1 forKey:@"na.search.guessyou.exposure"];
}

- (void)onClickedWithGuessYouKey:(IMYNASearchGuessYouKeyModel *)keyModel {
    if (keyModel.ad_model) {
        // 广告点击
        if (!keyModel.ad_isUriPush) {
            // 创建历史记录模型，将源模型转换为字典
            NSDictionary *sourceDict = [keyModel imy_jsonObject];
            IMYNASearchHistoryModel *historyModel = [IMYNASearchHistoryModel modelWithKeyword:keyModel.keyword sourceModel:sourceDict];
            [self searchWithKeyWord:keyModel.keyword wordsType:SYSearchWordTypeRecommend location:@"猜你想搜" historyModel:historyModel];
        }
        if (self.adClickSignal) {
            NSMutableDictionary *adInfo = [NSMutableDictionary dictionary];
            [adInfo setValue:keyModel.keyword forKey:@"content"];
            [adInfo setValue:@(6702) forKey:@"position"];
            [adInfo setValue:@(keyModel.ad_index) forKey:@"index"];
            [adInfo setValue:self.search_key forKey:@"search_key"];
            [self.adClickSignal sendData:adInfo];
        }
    } else {
        // 业务点击
        if (keyModel.click_ping.length > 0) {
            [[IMYServerRequest getPath:keyModel.click_ping host:nil params:nil headers:nil] subscribeCompleted:^{
                // OK
            }];
        }
        if (keyModel.scheme_uri.length > 0) {
            // 执行URI协议
            [[IMYURIManager shareURIManager] runActionWithString:keyModel.scheme_uri];
        } else {
            NSDictionary *sourceDict = [keyModel imy_jsonObject];
            IMYNASearchHistoryModel *historyModel = [IMYNASearchHistoryModel modelWithKeyword:keyModel.keyword sourceModel:sourceDict];
            [self searchWithKeyWord:keyModel.keyword wordsType:SYSearchWordTypeKeyword location:@"猜你想搜" historyModel:historyModel];
        }
    }
    // 埋点
    NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
    gaParams[@"func"] = @(23);
    gaParams[@"pos_id"] = @(self.pos_id);
    gaParams[@"location"] = @"猜你想搜";
    gaParams[@"location_index"] = @(keyModel.refresh_index + 1);
    gaParams[@"key"] = keyModel.keyword;
    gaParams[@"words_type"] = @(SYSearchWordTypeKeyword);
    gaParams[@"searcy_key"] = self.search_key;
    gaParams[@"info_type"] = @(self.info_type);
    [IMYGAEventHelper postWithPath:@"search-static" params:gaParams headers:nil completed:nil];
}

- (void)onExposuredWithHotspotsKey:(IMYNASearchHotspotsKeyModel *)keyModel {
    // 广告会全部曝光，内部自己判断是否广告model
    if (self.adShowSignal) {
        NSMutableDictionary *adInfo = [NSMutableDictionary dictionary];
        [adInfo setValue:@(6703) forKey:@"position"];
        [adInfo setValue:@(keyModel.index) forKey:@"index"];
        [adInfo setValue:@(keyModel.ad_model) forKey:@"isAd"];
        [adInfo setValue:keyModel.show_word forKey:@"content"];
        [adInfo setValue:self.search_key forKey:@"search_key"];
        [self.adShowSignal sendData:adInfo];
    }
    // 业务model自带的曝光监测
    if (!keyModel.ad_model && keyModel.show_ping.length > 0) {
        [[IMYServerRequest getPath:keyModel.show_ping host:nil params:nil headers:nil] subscribeCompleted:^{
            // OK
        }];
    }
    
    // 搜索热点，单独曝光
    NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
    gaParams[@"func"] = @(22);
    gaParams[@"pos_id"] = @(self.pos_id);
    gaParams[@"location"] = @"搜索热点";
    gaParams[@"location_index"] = @(keyModel.index + 1);
    gaParams[@"words"] = @[keyModel.show_word];
    gaParams[@"words_type"] = @(SYSearchWordTypeKeyword);
    gaParams[@"searcy_key"] = self.search_key;
    gaParams[@"info_type"] = @(self.info_type);
    [IMYGAEventHelper postWithPath:@"search-static" params:gaParams headers:nil completed:nil];
}

- (void)onClickedWithHotspotsKey:(IMYNASearchHotspotsKeyModel *)keyModel {
    if (keyModel.ad_model) {
        // 广告点击
        if (!keyModel.ad_isUriPush) {
            [self searchWithKeyWord:keyModel.query_word wordsType:SYSearchWordTypeRecommend location:@"搜索热点"];
        }
        if (self.adClickSignal) {
            NSMutableDictionary *adInfo = [NSMutableDictionary dictionary];
            [adInfo setValue:keyModel.show_word forKey:@"content"];
            [adInfo setValue:@(6703) forKey:@"position"];
            [adInfo setValue:@(keyModel.ad_index) forKey:@"index"];
            [adInfo setValue:self.search_key forKey:@"search_key"];
            [self.adClickSignal sendData:adInfo];
        }
    } else {
        // 业务点击
        if (keyModel.click_ping.length > 0) {
            [[IMYServerRequest getPath:keyModel.click_ping host:nil params:nil headers:nil] subscribeCompleted:^{
                // OK
            }];
        }
        if (keyModel.scheme_uri.length > 0) {
            // 执行URI协议
            [[IMYURIManager shareURIManager] runActionWithString:keyModel.scheme_uri];
        } else {
            // 开始搜索
            [self searchWithKeyWord:keyModel.query_word wordsType:SYSearchWordTypeKeyword location:@"搜索热点"];
        }
    }
    
    // 埋点
    NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
    gaParams[@"func"] = @(23);
    gaParams[@"pos_id"] = @(self.pos_id);
    gaParams[@"location"] = @"搜索热点";
    gaParams[@"location_index"] = @(keyModel.index + 1);
    gaParams[@"key"] = keyModel.show_word;
    gaParams[@"words_type"] = @(SYSearchWordTypeKeyword);
    gaParams[@"searcy_key"] = self.search_key;
    gaParams[@"info_type"] = @(self.info_type);
    [IMYGAEventHelper postWithPath:@"search-static" params:gaParams headers:nil completed:nil];
}

- (NSMutableArray *)historyExposuredKeys {
    if (!_historyExposuredKeys) {
        _historyExposuredKeys = [NSMutableArray array];
    }
    return _historyExposuredKeys;
}

- (NSMutableArray *)guessYouExposuredKeys {
    if (!_guessYouExposuredKeys) {
        _guessYouExposuredKeys = [NSMutableArray array];
    }
    return _guessYouExposuredKeys;
}

// 热门讨论曝光处理
- (void)onExposuredWithHotDiscussionKey:(IMYNASearchHotDiscussionKeyModel *)keyModel {
    // 热门讨论曝光埋点
    NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
    gaParams[@"func"] = @(22);
    gaParams[@"pos_id"] = @(self.pos_id);
    gaParams[@"location"] = @"热门讨论";
    gaParams[@"location_index"] = @(keyModel.index + 1);
    gaParams[@"words"] = @[keyModel.show_word];
    gaParams[@"words_type"] = @(SYSearchWordTypeRecommend);
    gaParams[@"searcy_key"] = self.search_key;
    gaParams[@"info_type"] = @(self.info_type);
    [IMYGAEventHelper postWithPath:@"search-static" params:gaParams headers:nil completed:nil];
}

// 热门讨论点击处理
- (void)onClickedWithHotDiscussionKey:(IMYNASearchHotDiscussionKeyModel *)keyModel {

    if (keyModel.scheme_uri.length > 0) {
        // 执行URI协议
        [[IMYURIManager shareURIManager] runActionWithString:keyModel.scheme_uri];
    }
    /*
     else {
         NSDictionary *sourceDict = [keyModel imy_jsonObject];
         IMYNASearchHistoryModel *historyModel = [IMYNASearchHistoryModel modelWithKeyword:keyModel.query_word sourceModel:sourceDict];
         [self searchWithKeyWord:keyModel.query_word wordsType:SYSearchWordTypeKeyword location:@"猜你想搜" historyModel:historyModel];
     }
     */
    // 热门讨论点击埋点
    NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
    gaParams[@"func"] = @(23);
    gaParams[@"pos_id"] = @(self.pos_id);
    gaParams[@"location"] = @"热门讨论";
    gaParams[@"key"] = keyModel.show_word;
    gaParams[@"location_index"] = @(keyModel.index + 1);
    gaParams[@"key"] = keyModel.show_word;
    gaParams[@"words_type"] = @(SYSearchWordTypeKeyword);
    gaParams[@"searcy_key"] = self.search_key;
    gaParams[@"info_type"] = @(self.info_type);

    [IMYGAEventHelper postWithPath:@"search-static" params:gaParams headers:nil completed:nil];
}

#pragma mark - UIScrollView

- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView {
    [self.searchBar.textField resignFirstResponder];
}

#pragma mark - Page GA Params

- (NSDictionary *)ga_appendParams {
    NSMutableDictionary *params = [NSMutableDictionary dictionaryWithDictionary:super.ga_appendParams];
    params[@"searcy_key"] = self.search_key;
    params[@"info_type"] = @(self.info_type);
    return params;
}

#pragma mark - Private Methods

/// 底纹词曝光（参考 IMYCKSearchHelper gaPostSearchExposureWithSearchText: 的参数口径）
/// 仅在搜索栏可见且与上次曝光词不同的情况下上报
- (void)postPlaceholderExposureForKeyword:(NSString *)keyword sourceModel:(NSDictionary *)sourceModel {
    // 基本校验
    if (![keyword isKindOfClass:NSString.class] || keyword.length == 0) {
        return;
    }
    // 搜索栏可见性校验（窗口存在且未隐藏）
    UIView *searchBarView = self.searchBar;
    if (!searchBarView || searchBarView.isHidden || searchBarView.alpha <= 0.01 || searchBarView.window == nil) {
        return;
    }
    // 去重：避免同一词重复上报
    if ([self.lastExposedPlaceholderKeyword isKindOfClass:NSString.class] && [self.lastExposedPlaceholderKeyword isEqualToString:keyword]) {
        return;
    }

    // head_search_words 数组非空校验 - 前置条件检查
    __block NSArray *headWords = nil;
    if ([self.searchSuggestWords isKindOfClass:NSDictionary.class]) {
        headWords = [self.searchSuggestWords valueForKey:@"head_search_words"];
    }
    if (![headWords isKindOfClass:NSArray.class] || headWords.count == 0) {
        return;
    }

    // shading_index: 当前 keyword 在 head_search_words 中的位置（1开始）。
    // 注意：本类在 preprocessHeadSearchWords 里可能已把对象数组转换为字符串数组，这里按字符串匹配容错。
    __block NSUInteger shadingIndex = 0;
    // 由于前面已经校验了 headWords 非空，这里直接遍历
    [headWords enumerateObjectsUsingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            if ([obj isKindOfClass:NSString.class]) {
                if ([(NSString *)obj isEqualToString:keyword]) {
                    shadingIndex = idx + 1;
                    *stop = YES;
                }
            } else if ([obj isKindOfClass:NSDictionary.class]) {
                NSString *kw = [(NSDictionary *)obj objectForKey:@"keyword"];
                if ([kw isKindOfClass:NSString.class] && [kw isEqualToString:keyword]) {
                    shadingIndex = idx + 1;
                    *stop = YES;
                }
            }
        }];

    // shading_type: 优先取 sourceModel.data_type；其次取当前 self.searchPlaceholder.data_type；否则 0
    id shadingType = nil;
    if ([sourceModel isKindOfClass:NSDictionary.class]) {
        shadingType = [sourceModel objectForKey:@"data_type"];
    }
    if (!shadingType && [self.searchPlaceholder isKindOfClass:NSDictionary.class]) {
        shadingType = [(NSDictionary *)self.searchPlaceholder objectForKey:@"data_type"];
    }
    if (!shadingType) {
        shadingType = @(0);
    }

    NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
    gaParams[@"func"] = @(22);
    gaParams[@"pos_id"] = @(self.pos_id);
    gaParams[@"words"] = keyword;
    gaParams[@"words_type"] = self.fromCommunity ? @(208) : @(1);
    gaParams[@"shading_location"] = @(2);
    gaParams[@"shading_index"] = @(shadingIndex);
    gaParams[@"shading_type"] = shadingType;

    [IMYGAEventHelper postWithPath:@"search-static" params:gaParams headers:nil completed:nil];

    // 记录去重关键词
    self.lastExposedPlaceholderKeyword = keyword;
}

/// 对外：设置底纹词并刷新搜索栏（不处理图标）
/// 传入可为 NSString（keyword）或 NSDictionary（需包含 keyword / 可选 data_type）
- (void)applySearchPlaceholderAndRefreshUI:(id)placeholderModel {
    // 仅设置底纹词与占位文本，不做图标处理
    if (!placeholderModel) { return; }
    // 统一进入已有 setter，容错字典/字符串
    self.searchPlaceholder = placeholderModel; // 中文注释：仅更新模型
    NSString *showKeyword = [self getKeywordFromSearchPlaceholder] ?: self.placeholderText;
    self.searchBar.textField.placeholder = showKeyword; // 中文注释：刷新占位显示
}

/// 底纹词点击上报（单独发送一条埋点）
- (void)postShadingClickForKeyword:(NSString *)keyword sourceModel:(id)sourceModel {
    // 基本校验
    if (![keyword isKindOfClass:NSString.class] || keyword.length == 0) {
        return;
    }
    
    // 搜索栏可见性校验（窗口存在且未隐藏）
    UIView *searchBarView = self.searchBar;
    if (!searchBarView || searchBarView.isHidden || searchBarView.alpha <= 0.01 || searchBarView.window == nil) {
        return;
    }
    
    // shading_index: 当前 keyword 在 head_search_words 中的位置（1开始）
    __block NSUInteger shadingIndex = 0;
    __block NSArray *headWords = nil;
    if ([self.searchSuggestWords isKindOfClass:NSDictionary.class]) {
        headWords = [self.searchSuggestWords valueForKey:@"head_search_words"];
    }
    if ([headWords isKindOfClass:NSArray.class] && headWords.count > 0) {
        [headWords enumerateObjectsUsingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            if ([obj isKindOfClass:NSString.class]) {
                if ([(NSString *)obj isEqualToString:keyword]) {
                    shadingIndex = idx + 1;
                    *stop = YES;
                }
            } else if ([obj isKindOfClass:NSDictionary.class]) {
                NSString *kw = [(NSDictionary *)obj objectForKey:@"keyword"];
                if ([kw isKindOfClass:NSString.class] && [kw isEqualToString:keyword]) {
                    shadingIndex = idx + 1;
                    *stop = YES;
                }
            }
        }];
    }
    
    // shading_type: 优先取 sourceModel.data_type；其次取当前 self.searchPlaceholder.data_type；否则 0
    id shadingType = nil;
    if ([sourceModel isKindOfClass:NSDictionary.class]) {
        shadingType = [sourceModel objectForKey:@"data_type"];
    }
    if (!shadingType && [self.searchPlaceholder isKindOfClass:NSDictionary.class]) {
        shadingType = [(NSDictionary *)self.searchPlaceholder objectForKey:@"data_type"];
    }
    if (!shadingType) {
        shadingType = @(0);
    }
    
    // 底纹词点击埋点（单独发送）
    NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
    gaParams[@"func"] = @(23);
    gaParams[@"pos_id"] = @(self.pos_id);
    gaParams[@"words"] = keyword;
    gaParams[@"words_type"] = @(1);
    gaParams[@"shading_location"] = @(2);
    gaParams[@"shading_index"] = @(shadingIndex);
    gaParams[@"shading_type"] = shadingType;
    gaParams[@"searcy_key"] = self.search_key;
    gaParams[@"info_type"] = @(self.info_type);
    
    [IMYGAEventHelper postWithPath:@"search-static" params:gaParams headers:nil completed:nil];
}

/// 检查手势位置是否在 tab 容器区域内
- (BOOL)isLocationInTabContainer:(CGPoint)location {
    // 检查是否在 hotspotsView（现在是 IMYNASearchTabContainerView）区域内
    if (self.tabContainerView && !self.tabContainerView.hidden && self.tabContainerView.alpha > 0) {
        CGRect hotspotsFrame = [self.tabContainerView convertRect:self.tabContainerView.bounds toView:self.view];
        if (CGRectContainsPoint(hotspotsFrame, location)) {
            // 进一步检查是否在有 tab 切换功能的区域内
            // 只有当实验开启且有 PageViewController 时才阻止侧滑返回
            NSInteger experimentValue = self.tabContainerView.searchHotExperimentValue;
            if (experimentValue == 1 || experimentValue == 2) {
                // 检查是否在 PageViewController 的内容区域内（而不是在 tab 标题栏）
                // 这样可以更精确地控制手势冲突
                CGRect pageViewFrame = hotspotsFrame;
                pageViewFrame.origin.y += 44; // tab 标题栏高度
                pageViewFrame.size.height -= 44;
                if (CGRectContainsPoint(pageViewFrame, location)) {
                    return YES; // 在 PageViewController 内容区域，阻止侧滑返回
                }
            }
        }
    }
    return NO;
}

/// 检查是否应该允许侧滑返回（考虑当前tab状态和滑动方向）
- (BOOL)shouldAllowPanDismissWithGestureRecognizer:(UIPanGestureRecognizer *)gestureRecognizer {
    // 检查手势是否在 tab 容器区域内
    CGPoint location = [gestureRecognizer locationInView:self.view];
    if (![self isLocationInTabContainer:location]) {
        return YES; // 不在tab容器内，允许侧滑返回
    }

    // 在tab容器内，需要进一步判断
    NSInteger experimentValue = self.tabContainerView.searchHotExperimentValue;
    if (experimentValue == 1 || experimentValue == 2) {
        // 获取滑动方向
        CGPoint velocity = [gestureRecognizer velocityInView:gestureRecognizer.view];
        BOOL isRightSwipe = velocity.x > 0;

        // 获取当前选中的tab（通过PageViewController的当前页面索引）
        NSInteger currentTabIndex = [self getCurrentTabIndex];

        // 如果当前在搜索热点tab（索引0）且向右滑动，允许侧滑返回
        if (currentTabIndex == 0 && isRightSwipe) {
            return YES;
        }

        // 其他情况不允许侧滑返回，让tab切换处理
        return NO;
    }

    return YES; // 未开启实验，允许侧滑返回
}

- (BOOL)fromCommunity {
    return [self.fromURI.params[@"search_home_type"] integerValue] == 1;
}

/// 获取当前选中的tab索引
- (NSInteger)getCurrentTabIndex {
    if ([self.tabContainerView respondsToSelector:@selector(pageViewController)]) {
        id pageViewController = [(id)self.tabContainerView pageViewController];
        if ([pageViewController respondsToSelector:@selector(currentPageIndex)]) {
            return [pageViewController currentPageIndex];
        }
    }
    return 0; // 默认返回搜索热点tab
}

- (void)dealloc {
    // 移除通知监听
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

@end
